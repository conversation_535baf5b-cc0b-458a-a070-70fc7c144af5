# Call Monitoring Implementation Plan

## Overview
This document outlines the comprehensive plan to implement extensive call monitoring functionality for the Voice AI agents management system. The system will track all call-related information including call IDs, user IDs, call lengths, transcripts, recordings, and other relevant data.

## Current Infrastructure Analysis

### Existing Components:
1. **Supabase Database**: Already configured with agents table and analytics
2. **Call Logging**: Basic call_log table exists with CallLogEntry model
3. **Daily.co Integration**: WebRTC calls through Daily transport
4. **Frontend**: React/Next.js with agent management UI
5. **Backend**: FastAPI server with agent and call log services

### Current Call Flow:
1. Agent starts in Daily room via `/` or `/connect` endpoints
2. Basic call logging in `call_log` table (room_url, start_time, end_time, duration, transcript)
3. Bot framework handles call lifecycle events
4. Transcript collection via Deepgram STT service

## Implementation Plan

### Phase 1: Enhanced Database Schema

#### 1.1 Create Enhanced call_history Table
- Replace/enhance existing call_log table
- Add comprehensive fields for monitoring
- Include user identification, call quality metrics, service usage tracking

#### 1.2 Database Schema Design
```sql
CREATE TABLE call_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    call_id TEXT UNIQUE NOT NULL,
    agent_id UUID REFERENCES agents(id) ON DELETE CASCADE,
    user_id TEXT,
    room_url TEXT NOT NULL,
    
    -- Call Timing
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE,
    call_duration_seconds INTEGER,
    
    -- Call Content
    full_transcript TEXT,
    call_recording_url TEXT,
    
    -- Call Quality & Metrics
    call_status TEXT DEFAULT 'active' CHECK (call_status IN ('active', 'completed', 'failed', 'abandoned')),
    call_quality_score DECIMAL(3,2), -- 1.00 to 5.00
    user_satisfaction_rating INTEGER, -- 1-5 stars
    
    -- Service Configuration Used
    stt_service TEXT,
    llm_service TEXT,
    tts_service TEXT,
    stt_config JSONB,
    llm_config JSONB,
    tts_config JSONB,
    
    -- Usage Metrics
    total_tokens_used INTEGER DEFAULT 0,
    stt_duration_seconds INTEGER DEFAULT 0,
    tts_characters_generated INTEGER DEFAULT 0,
    estimated_cost DECIMAL(10,4) DEFAULT 0.00,
    
    -- Technical Metrics
    average_response_time_ms INTEGER,
    error_count INTEGER DEFAULT 0,
    interruption_count INTEGER DEFAULT 0,
    
    -- Additional Data
    user_agent TEXT,
    ip_address INET,
    country_code TEXT,
    language_detected TEXT,
    conversation_summary TEXT,
    tags TEXT[],
    metadata JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 1.3 Supporting Tables
```sql
-- Call participants tracking
CREATE TABLE call_participants (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    call_id UUID REFERENCES call_history(id) ON DELETE CASCADE,
    participant_id TEXT NOT NULL,
    participant_type TEXT CHECK (participant_type IN ('user', 'agent', 'bot')),
    joined_at TIMESTAMP WITH TIME ZONE,
    left_at TIMESTAMP WITH TIME ZONE,
    duration_seconds INTEGER
);

-- Call events tracking
CREATE TABLE call_events (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    call_id UUID REFERENCES call_history(id) ON DELETE CASCADE,
    event_type TEXT NOT NULL,
    event_data JSONB,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Phase 2: Backend Implementation

#### 2.1 Enhanced Call History Models
- Update CallLogEntry model to CallHistory model
- Add comprehensive fields matching database schema
- Create CallParticipant and CallEvent models

#### 2.2 Enhanced Call History Service
- Extend CallLogService to CallHistoryService
- Add methods for comprehensive call tracking
- Implement call analytics and reporting methods
- Add real-time call monitoring capabilities

#### 2.3 Call Monitoring Integration
- Integrate with Daily.co webhooks for participant events
- Enhance bot framework to track detailed metrics
- Add call recording functionality via Daily.co
- Implement real-time transcript streaming

#### 2.4 API Endpoints
```python
# Call History Management
GET /api/calls/                    # List all calls with filtering
GET /api/calls/{call_id}          # Get specific call details
GET /api/calls/{call_id}/transcript # Get call transcript
GET /api/calls/{call_id}/recording  # Get call recording
POST /api/calls/{call_id}/rating   # Submit user rating

# Call Analytics
GET /api/analytics/calls          # Call analytics dashboard data
GET /api/analytics/agents/{agent_id}/calls # Agent-specific call analytics
GET /api/analytics/calls/summary  # Summary statistics

# Real-time Monitoring
GET /api/calls/active             # List active calls
WebSocket /ws/calls/{call_id}     # Real-time call monitoring
```

### Phase 3: Frontend Implementation

#### 3.1 Call History Page
- Create new route `/calls` for call history
- Implement comprehensive call listing with filters
- Add search functionality (by user, agent, date range)
- Include pagination and sorting

#### 3.2 Call Details Modal/Page
- Detailed call information display
- Transcript viewer with timestamps
- Audio player for call recordings
- Call metrics and quality indicators

#### 3.3 Call Analytics Dashboard
- Real-time call monitoring dashboard
- Call volume and duration charts
- Service usage analytics
- Cost tracking and reporting

#### 3.4 Agent-Specific Call History
- Integrate call history into agent dashboard
- Show recent calls for each agent
- Agent performance metrics

### Phase 4: Advanced Features

#### 4.1 Real-time Monitoring
- Live call dashboard showing active calls
- Real-time transcript streaming
- Call quality monitoring alerts

#### 4.2 Call Recording Integration
- Automatic call recording via Daily.co
- Secure storage and retrieval
- Privacy controls and retention policies

#### 4.3 Analytics and Reporting
- Comprehensive call analytics
- Export functionality (CSV, PDF reports)
- Automated reporting schedules

#### 4.4 User Feedback System
- Post-call rating collection
- Feedback forms and surveys
- Sentiment analysis of transcripts

## Technical Implementation Details

### Database Migration Strategy
1. Create new call_history table alongside existing call_log
2. Migrate existing data from call_log to call_history
3. Update all services to use new schema
4. Remove old call_log table after verification

### Daily.co Integration Enhancements
1. Configure webhooks for participant events
2. Enable call recording in room creation
3. Implement recording retrieval and storage
4. Add call quality monitoring

### Security Considerations
1. Encrypt sensitive call data
2. Implement proper access controls
3. Add data retention policies
4. Ensure GDPR/privacy compliance

### Performance Optimizations
1. Database indexing strategy
2. Efficient pagination for large datasets
3. Caching for frequently accessed data
4. Background processing for analytics

## Implementation Timeline

### Week 1: Database & Backend Foundation
- Create enhanced database schema
- Implement new models and services
- Set up basic API endpoints

### Week 2: Call Monitoring Integration
- Integrate with Daily.co webhooks
- Enhance bot framework for detailed tracking
- Implement call recording functionality

### Week 3: Frontend Development
- Create call history page
- Implement call details views
- Add basic analytics dashboard

### Week 4: Advanced Features & Polish
- Real-time monitoring features
- Advanced analytics and reporting
- Testing and optimization

## Success Metrics
1. Complete call lifecycle tracking
2. Real-time call monitoring capabilities
3. Comprehensive analytics and reporting
4. User-friendly call history interface
5. Scalable architecture for future enhancements

## Next Steps
1. Review and approve this plan
2. Set up development environment
3. Begin Phase 1 implementation
4. Regular progress reviews and adjustments
