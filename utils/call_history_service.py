from typing import List, Optional, Dict, Any
from fastapi import HTTPException
from supabase import Client
from loguru import logger
from datetime import datetime, timezone
import uuid

from .call_history_model import (
    CallHistory, CallParticipant, CallEvent, CallAnalyticsSummary,
    ActiveCall, CallHistoryFilter, CallHistoryResponse, CallRating,
    CallMetricsUpdate, CallStatus, ParticipantType
)

class CallHistoryService:
    """Enhanced service for comprehensive call tracking and monitoring"""

    def __init__(self, supabase_client: Client):
        self.db = supabase_client

    async def create_call(self, call_data: CallHistory) -> CallHistory:
        """Create a new call history entry"""
        try:
            # Generate call_id if not provided
            if not call_data.call_id:
                call_data.call_id = str(uuid.uuid4())

            # Ensure start_time is set
            if not call_data.start_time:
                call_data.start_time = datetime.now(timezone.utc)

            data = (
                self.db.table("call_history")
                .insert(call_data.model_dump(exclude_unset=True, exclude_none=True))
                .execute()
            )

            logger.info(f"Created call history entry with ID: {call_data.call_id}")
            return CallHistory.model_validate(data.data[0])

        except Exception as e:
            logger.error(f"Failed to create call history entry: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to create call: {str(e)}")

    async def update_call(self, call_id: str, updates: Dict[str, Any]) -> Optional[CallHistory]:
        """Update an existing call history entry"""
        try:
            # Add updated_at timestamp
            updates["updated_at"] = datetime.now(timezone.utc).isoformat()

            data = (
                self.db.table("call_history")
                .update(updates)
                .eq("call_id", call_id)
                .execute()
            )

            if not data.data:
                logger.warning(f"Call not found for update: {call_id}")
                return None

            logger.info(f"Updated call history: {call_id}")
            return CallHistory.model_validate(data.data[0])

        except Exception as e:
            logger.error(f"Failed to update call {call_id}: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to update call: {str(e)}")

    async def get_call(self, call_id: str) -> Optional[CallHistory]:
        """Get a specific call by call_id"""
        try:
            data = (
                self.db.table("call_history")
                .select("*")
                .eq("call_id", call_id)
                .execute()
            )

            if not data.data:
                return None

            return CallHistory.model_validate(data.data[0])

        except Exception as e:
            logger.error(f"Failed to get call {call_id}: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to get call: {str(e)}")

    async def get_calls(self, filters: CallHistoryFilter) -> CallHistoryResponse:
        """Get paginated list of calls with filtering"""
        try:
            # Build query
            query = self.db.table("call_history").select("*", count="exact")

            # Apply filters
            if filters.agent_id:
                query = query.eq("agent_id", filters.agent_id)
            if filters.user_id:
                query = query.eq("user_id", filters.user_id)
            if filters.call_status:
                status_value = filters.call_status.value if hasattr(filters.call_status, 'value') else filters.call_status
                query = query.eq("call_status", status_value)
            if filters.start_date:
                query = query.gte("start_time", filters.start_date.isoformat())
            if filters.end_date:
                query = query.lte("start_time", filters.end_date.isoformat())
            if filters.min_duration:
                query = query.gte("call_duration_seconds", filters.min_duration)
            if filters.max_duration:
                query = query.lte("call_duration_seconds", filters.max_duration)
            if filters.search_query:
                query = query.ilike("full_transcript", f"%{filters.search_query}%")
            if filters.tags:
                query = query.contains("tags", filters.tags)

            # Apply sorting
            if filters.sort_order == "desc":
                query = query.order(filters.sort_by, desc=True)
            else:
                query = query.order(filters.sort_by)

            # Apply pagination
            offset = (filters.page - 1) * filters.page_size
            query = query.range(offset, offset + filters.page_size - 1)

            data = query.execute()

            calls = [CallHistory.model_validate(call) for call in data.data]
            total_count = data.count or 0
            total_pages = (total_count + filters.page_size - 1) // filters.page_size

            return CallHistoryResponse(
                calls=calls,
                total_count=total_count,
                page=filters.page,
                page_size=filters.page_size,
                total_pages=total_pages,
                has_next=filters.page < total_pages,
                has_previous=filters.page > 1
            )

        except Exception as e:
            logger.error(f"Failed to get calls: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to get calls: {str(e)}")

    async def end_call(self, call_id: str, end_time: Optional[datetime] = None) -> Optional[CallHistory]:
        """End a call and calculate final metrics"""
        try:
            if not end_time:
                end_time = datetime.now(timezone.utc)

            # Get current call data to calculate duration
            call = await self.get_call(call_id)
            if not call:
                return None

            duration_seconds = None
            if call.start_time:
                duration_seconds = int((end_time - call.start_time).total_seconds())

            updates = {
                "end_time": end_time.isoformat(),
                "call_duration_seconds": duration_seconds,
                "call_status": CallStatus.COMPLETED.value,
                "updated_at": end_time.isoformat()
            }

            return await self.update_call(call_id, updates)

        except Exception as e:
            logger.error(f"Failed to end call {call_id}: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to end call: {str(e)}")

    async def add_participant(self, participant: CallParticipant) -> CallParticipant:
        """Add a participant to a call"""
        try:
            data = (
                self.db.table("call_participants")
                .insert(participant.model_dump(exclude_unset=True, exclude_none=True))
                .execute()
            )

            logger.info(f"Added participant {participant.participant_id} to call {participant.call_id}")
            return CallParticipant.model_validate(data.data[0])

        except Exception as e:
            logger.error(f"Failed to add participant: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to add participant: {str(e)}")

    async def remove_participant(self, call_id: str, participant_id: str, left_at: Optional[datetime] = None) -> bool:
        """Remove a participant from a call"""
        try:
            if not left_at:
                left_at = datetime.now(timezone.utc)

            # Calculate duration if participant has joined_at
            participant_data = (
                self.db.table("call_participants")
                .select("joined_at")
                .eq("call_id", call_id)
                .eq("participant_id", participant_id)
                .execute()
            )

            duration_seconds = None
            if participant_data.data and participant_data.data[0].get("joined_at"):
                joined_at = datetime.fromisoformat(participant_data.data[0]["joined_at"])
                duration_seconds = int((left_at - joined_at).total_seconds())

            data = (
                self.db.table("call_participants")
                .update({
                    "left_at": left_at.isoformat(),
                    "duration_seconds": duration_seconds
                })
                .eq("call_id", call_id)
                .eq("participant_id", participant_id)
                .execute()
            )

            logger.info(f"Removed participant {participant_id} from call {call_id}")
            return len(data.data) > 0

        except Exception as e:
            logger.error(f"Failed to remove participant: {e}")
            return False

    async def add_event(self, event: CallEvent) -> CallEvent:
        """Add an event to a call"""
        try:
            if not event.timestamp:
                event.timestamp = datetime.now(timezone.utc)

            data = (
                self.db.table("call_events")
                .insert(event.model_dump(exclude_unset=True, exclude_none=True))
                .execute()
            )

            logger.debug(f"Added event {event.event_type} to call {event.call_id}")
            return CallEvent.model_validate(data.data[0])

        except Exception as e:
            logger.error(f"Failed to add event: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to add event: {str(e)}")

    async def get_active_calls(self) -> List[ActiveCall]:
        """Get all currently active calls"""
        try:
            data = (
                self.db.from_("active_calls")
                .select("*")
                .execute()
            )

            return [ActiveCall.model_validate(call) for call in data.data]

        except Exception as e:
            logger.error(f"Failed to get active calls: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to get active calls: {str(e)}")

    async def get_call_analytics(self, agent_id: Optional[str] = None, days: int = 30) -> List[CallAnalyticsSummary]:
        """Get call analytics summary"""
        try:
            query = (
                self.db.from_("call_analytics_summary")
                .select("*")
                .gte("call_date", f"CURRENT_DATE - INTERVAL '{days} days'")
            )

            if agent_id:
                query = query.eq("agent_id", agent_id)

            data = query.order("call_date", desc=True).execute()

            return [CallAnalyticsSummary.model_validate(summary) for summary in data.data]

        except Exception as e:
            logger.error(f"Failed to get call analytics: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to get analytics: {str(e)}")

    async def update_call_metrics(self, metrics: CallMetricsUpdate) -> Optional[CallHistory]:
        """Update call metrics during or after a call"""
        try:
            updates = metrics.model_dump(exclude_unset=True, exclude_none=True, exclude={"call_id"})
            return await self.update_call(metrics.call_id, updates)

        except Exception as e:
            logger.error(f"Failed to update call metrics: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to update metrics: {str(e)}")

    async def submit_call_rating(self, rating: CallRating) -> Optional[CallHistory]:
        """Submit user rating for a call"""
        try:
            updates = {
                "user_satisfaction_rating": rating.user_satisfaction_rating
            }

            if rating.feedback_text:
                # Store feedback in metadata
                call = await self.get_call(rating.call_id)
                if call:
                    metadata = call.metadata or {}
                    metadata["user_feedback"] = rating.feedback_text
                    if rating.quality_issues:
                        metadata["quality_issues"] = rating.quality_issues
                    updates["metadata"] = metadata

            return await self.update_call(rating.call_id, updates)

        except Exception as e:
            logger.error(f"Failed to submit call rating: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to submit rating: {str(e)}")

# Backward compatibility
class CallLogService(CallHistoryService):
    """Legacy service - use CallHistoryService instead"""
    pass
