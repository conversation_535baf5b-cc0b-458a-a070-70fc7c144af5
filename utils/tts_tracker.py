"""
TTS Tracker - Processor to track all TTS output for call transcripts
"""

from typing import Optional, Callable
from loguru import logger
from pipecat.frames.frames import Frame, TTSSpeakFrame, TTSAudioRawFrame, TTSStartedFrame, TTSStoppedFrame
from pipecat.processors.frame_processor import FrameDirection, FrameProcessor


class TTSTracker(FrameProcessor):
    """
    Processor that tracks all TTS output to build comprehensive agent transcripts.
    This processor sits in the pipeline and intercepts TTS frames to capture
    what the agent is saying.
    """
    
    def __init__(self, callback: Optional[Callable[[str], None]] = None):
        """
        Initialize TTS tracker.
        
        Args:
            callback: Optional callback function to call when TTS text is detected.
                     Should accept a string parameter (the TTS text).
        """
        super().__init__()
        self._callback = callback
        self._current_text = ""
        
    def set_callback(self, callback: Callable[[str], None]):
        """Set or update the callback function."""
        self._callback = callback
        
    async def process_frame(self, frame: Frame, direction: FrameDirection) -> Frame:
        """Process frames and track TTS output."""
        
        # Track TTSSpeakFrame - this contains the text that will be spoken
        if isinstance(frame, TTSSpeakFrame):
            text = frame.text
            if text and text.strip():
                logger.debug(f"TTSTracker: Captured TTS text: '{text.strip()}'")
                self._current_text = text.strip()
                
                # Call the callback if provided
                if self._callback:
                    try:
                        self._callback(text.strip())
                    except Exception as e:
                        logger.error(f"TTSTracker: Error in callback: {e}")
        
        # Track TTS lifecycle events for debugging
        elif isinstance(frame, TTSStartedFrame):
            logger.debug(f"TTSTracker: TTS started for text: '{self._current_text}'")
            
        elif isinstance(frame, TTSStoppedFrame):
            logger.debug(f"TTSTracker: TTS stopped for text: '{self._current_text}'")
            self._current_text = ""
            
        # Pass the frame through unchanged
        return frame


class TranscriptCollector:
    """
    Collects and manages both user and agent transcripts with proper formatting.
    """
    
    def __init__(self):
        self.user_transcript = ""
        self.agent_transcript = ""
        self.full_transcript = ""
        self._conversation_log = []  # List of (speaker, text, timestamp) tuples
        
    def add_user_speech(self, text: str):
        """Add user speech to the transcript."""
        if text and text.strip():
            clean_text = text.strip()
            self.user_transcript += clean_text + " "
            self._add_to_conversation("User", clean_text)
            logger.debug(f"TranscriptCollector: Added user speech: '{clean_text}'")
            
    def add_agent_speech(self, text: str):
        """Add agent speech to the transcript."""
        if text and text.strip():
            clean_text = text.strip()
            self.agent_transcript += clean_text + " "
            self._add_to_conversation("Agent", clean_text)
            logger.debug(f"TranscriptCollector: Added agent speech: '{clean_text}'")
            
    def _add_to_conversation(self, speaker: str, text: str):
        """Add an entry to the conversation log."""
        import time
        self._conversation_log.append((speaker, text, time.time()))
        self._rebuild_full_transcript()
        
    def _rebuild_full_transcript(self):
        """Rebuild the full transcript from conversation log."""
        transcript_parts = []
        for speaker, text, timestamp in self._conversation_log:
            transcript_parts.append(f"{speaker}: {text}")
        self.full_transcript = "\n".join(transcript_parts)
        
    def get_formatted_transcript(self) -> str:
        """Get the full formatted transcript with speaker labels."""
        return self.full_transcript
        
    def get_user_transcript(self) -> str:
        """Get only the user's speech."""
        return self.user_transcript.strip()
        
    def get_agent_transcript(self) -> str:
        """Get only the agent's speech."""
        return self.agent_transcript.strip()
        
    def clear(self):
        """Clear all transcripts."""
        self.user_transcript = ""
        self.agent_transcript = ""
        self.full_transcript = ""
        self._conversation_log.clear()
        
    def get_stats(self) -> dict:
        """Get transcript statistics."""
        return {
            "user_word_count": len(self.user_transcript.split()) if self.user_transcript else 0,
            "agent_word_count": len(self.agent_transcript.split()) if self.agent_transcript else 0,
            "total_exchanges": len(self._conversation_log),
            "user_character_count": len(self.user_transcript),
            "agent_character_count": len(self.agent_transcript)
        }
