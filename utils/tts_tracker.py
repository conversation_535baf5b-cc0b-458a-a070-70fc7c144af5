"""
TTS Tracker - Processor to track all TTS output for call transcripts
"""

from typing import Optional, Callable
from loguru import logger
from pipecat.frames.frames import <PERSON>ame, TTSSpeakFrame, TTSAudioRawFrame, TTSStartedFrame, TTSStoppedFrame, TextFrame
from pipecat.processors.frame_processor import FrameDirection, FrameProcessor


class LLMResponseTracker(FrameProcessor):
    """
    Processor that tracks LLM text output to capture agent responses.
    This sits between LLM and TTS to intercept all agent responses.
    """

    def __init__(self, callback: Optional[Callable[[str], None]] = None):
        """
        Initialize LLM response tracker.

        Args:
            callback: Optional callback function to call when LLM text is detected.
                     Should accept a string parameter (the LLM response text).
        """
        super().__init__()
        self._callback = callback
        self._current_response = ""

    def set_callback(self, callback: Callable[[str], None]):
        """Set or update the callback function."""
        self._callback = callback

    async def process_frame(self, frame: Frame, direction: FrameDirection):
        """Process frames and track LLM text output."""
        await super().process_frame(frame, direction)

        # Track TextFrame - this contains LLM generated text
        if isinstance(frame, TextFrame):
            text = frame.text
            if text and text.strip():
                logger.debug(f"LLMResponseTracker: Captured LLM response: '{text.strip()}'")

                # Call the callback if provided (for building response)
                if self._callback:
                    try:
                        self._callback(text.strip())
                    except Exception as e:
                        logger.error(f"LLMResponseTracker: Error in callback: {e}")

        # Track TTSSpeakFrame to know when response is complete
        elif isinstance(frame, TTSSpeakFrame):
            # When we see a TTSSpeakFrame, it means the LLM response is complete
            # and ready to be spoken. This is our signal to finalize the response.
            logger.debug(f"LLMResponseTracker: Response complete, ready for TTS: '{frame.text}'")

            # Call callback with completion signal
            if self._callback:
                try:
                    # Signal that this response is complete
                    self._callback("", is_complete=True)
                except Exception as e:
                    logger.error(f"LLMResponseTracker: Error in completion callback: {e}")

        # Pass the frame through unchanged
        await self.push_frame(frame, direction)


class TTSTracker(FrameProcessor):
    """
    Processor that tracks all TTS output to build comprehensive agent transcripts.
    This processor sits in the pipeline and intercepts TTS frames to capture
    what the agent is saying.
    """

    def __init__(self, callback: Optional[Callable[[str], None]] = None):
        """
        Initialize TTS tracker.

        Args:
            callback: Optional callback function to call when TTS text is detected.
                     Should accept a string parameter (the TTS text).
        """
        super().__init__()
        self._callback = callback
        self._current_text = ""

    def set_callback(self, callback: Callable[[str], None]):
        """Set or update the callback function."""
        self._callback = callback

    async def process_frame(self, frame: Frame, direction: FrameDirection):
        """Process frames and track TTS output."""
        await super().process_frame(frame, direction)

        # Track TTSSpeakFrame - this contains the text that will be spoken
        if isinstance(frame, TTSSpeakFrame):
            text = frame.text
            if text and text.strip():
                logger.debug(f"TTSTracker: Captured TTS text: '{text.strip()}'")
                self._current_text = text.strip()

                # Call the callback if provided
                if self._callback:
                    try:
                        self._callback(text.strip())
                    except Exception as e:
                        logger.error(f"TTSTracker: Error in callback: {e}")

        # Track TTS lifecycle events for debugging
        elif isinstance(frame, TTSStartedFrame):
            logger.debug(f"TTSTracker: TTS started for text: '{self._current_text}'")

        elif isinstance(frame, TTSStoppedFrame):
            logger.debug(f"TTSTracker: TTS stopped for text: '{self._current_text}'")
            self._current_text = ""

        # Pass the frame through unchanged
        await self.push_frame(frame, direction)


class TranscriptCollector:
    """
    Collects and manages both user and agent transcripts with proper formatting.
    Handles Deepgram interim results to avoid duplication.
    """

    def __init__(self):
        self.user_transcript = ""
        self.agent_transcript = ""
        self.full_transcript = ""
        self._conversation_log = []  # List of (speaker, text, timestamp) tuples
        self._last_user_text = ""  # Track last user text to handle interim results
        self._current_agent_response = ""  # Track current agent response being built

    def add_user_speech(self, text: str, is_final: bool = True):
        """
        Add user speech to the transcript with deduplication.

        Args:
            text: The transcript text from STT
            is_final: Whether this is a final result or interim
        """
        if not text or not text.strip():
            return

        clean_text = text.strip()

        # Handle interim vs final results to avoid duplication
        if is_final:
            # Only add if this is different from the last text we added
            if clean_text != self._last_user_text:
                # If we have a previous incomplete text, replace it
                if self._last_user_text and not self._last_user_text.endswith('.') and not self._last_user_text.endswith('?') and not self._last_user_text.endswith('!'):
                    # Remove the last incomplete entry from conversation log
                    if self._conversation_log and self._conversation_log[-1][0] == "User":
                        self._conversation_log.pop()
                        # Also remove from user_transcript
                        if self._last_user_text in self.user_transcript:
                            self.user_transcript = self.user_transcript.replace(self._last_user_text + " ", "")

                self.user_transcript += clean_text + " "
                self._add_to_conversation("User", clean_text)
                self._last_user_text = clean_text
                logger.debug(f"TranscriptCollector: Added final user speech: '{clean_text}'")
        else:
            # For interim results, just update our tracking but don't add to transcript yet
            logger.debug(f"TranscriptCollector: Interim user speech: '{clean_text}'")

    def add_agent_speech(self, text: str, is_complete: bool = False):
        """
        Add agent speech to the transcript.

        Args:
            text: The agent response text
            is_complete: Whether this completes the current response
        """
        if not text or not text.strip():
            return

        clean_text = text.strip()

        # Build up the current response
        if not is_complete:
            # Add space between tokens for readability
            if self._current_agent_response and not self._current_agent_response.endswith(' '):
                self._current_agent_response += " "
            self._current_agent_response += clean_text
            logger.debug(f"TranscriptCollector: Building agent response: '{self._current_agent_response}'")
        else:
            # Complete response - add to transcript
            if self._current_agent_response:
                complete_response = self._current_agent_response.strip()
                self.agent_transcript += complete_response + " "
                self._add_to_conversation("Agent", complete_response)
                logger.debug(f"TranscriptCollector: Added complete agent speech: '{complete_response}'")
                self._current_agent_response = ""
            else:
                # Single complete text
                self.agent_transcript += clean_text + " "
                self._add_to_conversation("Agent", clean_text)
                logger.debug(f"TranscriptCollector: Added agent speech: '{clean_text}'")

    def _add_to_conversation(self, speaker: str, text: str):
        """Add an entry to the conversation log."""
        import time
        self._conversation_log.append((speaker, text, time.time()))
        self._rebuild_full_transcript()

    def _rebuild_full_transcript(self):
        """Rebuild the full transcript from conversation log."""
        transcript_parts = []
        for speaker, text, timestamp in self._conversation_log:
            transcript_parts.append(f"{speaker}: {text}")
        self.full_transcript = "\n".join(transcript_parts)

    def get_formatted_transcript(self) -> str:
        """Get the full formatted transcript with speaker labels."""
        return self.full_transcript

    def get_user_transcript(self) -> str:
        """Get only the user's speech."""
        return self.user_transcript.strip()

    def get_agent_transcript(self) -> str:
        """Get only the agent's speech."""
        return self.agent_transcript.strip()

    def clear(self):
        """Clear all transcripts."""
        self.user_transcript = ""
        self.agent_transcript = ""
        self.full_transcript = ""
        self._conversation_log.clear()

    def get_stats(self) -> dict:
        """Get transcript statistics."""
        return {
            "user_word_count": len(self.user_transcript.split()) if self.user_transcript else 0,
            "agent_word_count": len(self.agent_transcript.split()) if self.agent_transcript else 0,
            "total_exchanges": len(self._conversation_log),
            "user_character_count": len(self.user_transcript),
            "agent_character_count": len(self.agent_transcript)
        }
