"""
Recording service for managing Daily.co call recordings.
Handles recording retrieval, storage, and integration with call history.
"""

import asyncio
from typing import Optional, Dict, List
from datetime import datetime, timezone
from loguru import logger
from supabase import Client

from utils.custom_daily_helpers import CustomDailyRESTHelper


class RecordingService:
    """Service for managing call recordings through Daily.co API."""

    def __init__(self, supabase_client: Client, daily_helper: CustomDailyRESTHelper):
        """
        Initialize the recording service.

        Args:
            supabase_client: Supabase client for database operations
            daily_helper: Daily REST helper for API operations
        """
        self.db = supabase_client
        self.daily_helper = daily_helper

    async def get_recording_for_room(self, room_url: str, max_retries: int = 10, retry_delay: int = 30) -> Optional[str]:
        """
        Get recording URL for a specific room with retry logic.

        Args:
            room_url: Daily room URL
            max_retries: Maximum number of retry attempts
            retry_delay: Delay between retries in seconds

        Returns:
            Recording URL if found, None otherwise
        """
        room_name = self.daily_helper.daily_rest_helper.get_name_from_url(room_url)

        for attempt in range(max_retries):
            try:
                logger.debug(f"Attempting to get recording for room {room_name}, attempt {attempt + 1}/{max_retries}")

                # Get recordings for this room
                recordings = await self.daily_helper.get_recordings(room_name=room_name, limit=10)

                if recordings:
                    # Find the most recent recording for this room
                    for recording in recordings:
                        if recording.get("room_name") == room_name and recording.get("status") == "finished":
                            recording_id = recording.get("id")
                            if recording_id:
                                # Get the access link for this recording
                                access_link = await self.daily_helper.get_recording_access_link(recording_id)
                                if access_link:
                                    logger.info(f"Found recording for room {room_name}: {access_link}")
                                    return access_link

                if attempt < max_retries - 1:
                    logger.debug(f"No recording found for room {room_name}, retrying in {retry_delay} seconds...")
                    await asyncio.sleep(retry_delay)
                else:
                    logger.warning(f"No recording found for room {room_name} after {max_retries} attempts")

            except Exception as e:
                logger.error(f"Error getting recording for room {room_name}, attempt {attempt + 1}: {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(retry_delay)

        return None

    async def update_call_history_with_recording(self, call_id: str, recording_url: str) -> bool:
        """
        Update call history entry with recording URL.

        Args:
            call_id: Call ID to update
            recording_url: Recording URL to store

        Returns:
            True if successful, False otherwise
        """
        try:
            result = (
                self.db.table("call_history")
                .update({"call_recording_url": recording_url})
                .eq("call_id", call_id)
                .execute()
            )

            if result.data:
                logger.info(f"Updated call history {call_id} with recording URL")
                return True
            else:
                logger.warning(f"No call history found for call_id {call_id}")
                return False

        except Exception as e:
            logger.error(f"Failed to update call history {call_id} with recording: {e}")
            return False

    async def process_call_recording(self, room_url: str, call_id: str) -> Optional[str]:
        """
        Process recording for a completed call.

        Args:
            room_url: Daily room URL
            call_id: Call ID for the call history entry

        Returns:
            Recording URL if found and stored, None otherwise
        """
        try:
            # Get recording URL with retries
            recording_url = await self.get_recording_for_room(room_url)

            if recording_url:
                # Update call history with recording URL
                success = await self.update_call_history_with_recording(call_id, recording_url)
                if success:
                    logger.info(f"Successfully processed recording for call {call_id}")
                    return recording_url
                else:
                    logger.error(f"Failed to update call history for call {call_id}")
            else:
                logger.warning(f"No recording found for call {call_id} in room {room_url}")

        except Exception as e:
            logger.error(f"Error processing recording for call {call_id}: {e}")

        return None

    async def get_all_recordings(self, limit: int = 100) -> List[Dict]:
        """
        Get all recordings from Daily.co.

        Args:
            limit: Maximum number of recordings to retrieve

        Returns:
            List of recording data
        """
        try:
            recordings = await self.daily_helper.get_recordings(limit=limit)
            logger.debug(f"Retrieved {len(recordings)} recordings from Daily.co")
            return recordings
        except Exception as e:
            logger.error(f"Error getting all recordings: {e}")
            return []

    async def get_recording_by_id(self, recording_id: str) -> Optional[Dict]:
        """
        Get specific recording by ID.

        Args:
            recording_id: Daily recording ID

        Returns:
            Recording data if found, None otherwise
        """
        try:
            recording = await self.daily_helper.get_recording_by_id(recording_id)
            if recording:
                logger.debug(f"Retrieved recording {recording_id}")
            else:
                logger.warning(f"Recording {recording_id} not found")
            return recording
        except Exception as e:
            logger.error(f"Error getting recording {recording_id}: {e}")
            return None

    async def cleanup_old_recordings(self, days_old: int = 30) -> int:
        """
        Clean up old recordings from Daily.co (if needed).
        Note: This would require additional Daily API endpoints for deletion.

        Args:
            days_old: Age threshold for cleanup

        Returns:
            Number of recordings cleaned up
        """
        # This is a placeholder for future implementation
        # Daily.co API would need to support recording deletion
        logger.info(f"Recording cleanup not implemented - would clean recordings older than {days_old} days")
        return 0

    async def get_recording_access_link(self, recording_id: str, valid_for_secs: int = 3600) -> Optional[str]:
        """
        Get access link for a specific recording.

        Args:
            recording_id: Daily recording ID
            valid_for_secs: Number of seconds the link should be valid for

        Returns:
            Download link if successful, None otherwise
        """
        try:
            access_link = await self.daily_helper.get_recording_access_link(recording_id, valid_for_secs)
            if access_link:
                logger.debug(f"Retrieved access link for recording {recording_id}")
            else:
                logger.warning(f"Access link not available for recording {recording_id}")
            return access_link
        except Exception as e:
            logger.error(f"Error getting recording access link for {recording_id}: {e}")
            return None
