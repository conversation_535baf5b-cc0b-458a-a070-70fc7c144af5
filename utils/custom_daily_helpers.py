from typing import Optional, List, Literal, Dict
from pydantic import BaseModel, Field
import time
import aiohttp
from urllib.parse import urlparse
from datetime import datetime
from pipecat.transports.services.helpers.daily_rest import DailyRESTHelper, DailyRoomParams, DailyRoomObject
from loguru import logger  # Import logger


# Define DailyMeetingTokenParams and related classes
class DailyMeetingTokenProperties(BaseModel):
    """Properties for configuring a Daily meeting token.

    Refer to the Daily API documentation for more information:
    https://docs.daily.co/reference/rest-api/meeting-tokens/create-meeting-token#properties
    """

    room_name: Optional[str] = Field(
        default=None,
        description="The room for which this token is valid. If not set, the token is valid for all rooms in your domain. You should always set room_name if using this token to control meeting access.",
    )

    eject_at_token_exp: Optional[bool] = Field(
        default=None,
        description="If `true`, the user will be ejected from the room when the token expires. Defaults to `false`.",
    )
    eject_after_elapsed: Optional[int] = Field(
        default=None,
        description="The number of seconds after which the user will be ejected from the room. If not provided, the user will not be ejected based on elapsed time.",
    )

    nbf: Optional[int] = Field(
        default=None,
        description="Not before. This is a unix timestamp (seconds since the epoch.) Users cannot join a meeting in with this token before this time.",
    )

    exp: Optional[int] = Field(
        default=None,
        description="Expiration time (unix timestamp in seconds). We strongly recommend setting this value for security. If not set, the token will not expire. Refer docs for more info.",
    )
    is_owner: Optional[bool] = Field(
        default=None,
        description="If `true`, the token will grant owner privileges in the room. Defaults to `false`.",
    )
    user_name: Optional[str] = Field(
        default=None,
        description="The name of the user. This will be added to the token payload.",
    )
    user_id: Optional[str] = Field(
        default=None,
        description="A unique identifier for the user. This will be added to the token payload.",
    )
    enable_screenshare: Optional[bool] = Field(
        default=None,
        description="If `true`, the user will be able to share their screen. Defaults to `true`.",
    )
    start_video_off: Optional[bool] = Field(
        default=None,
        description="If `true`, the user's video will be turned off when they join the room. Defaults to `false`.",
    )
    start_audio_off: Optional[bool] = Field(
        default=None,
        description="If `true`, the user's audio will be turned off when they join the room. Defaults to `false`.",
    )
    enable_recording: Optional[Literal["cloud", "local", "raw-tracks"]] = Field(
        default=None,
        description="Recording settings for the token. Must be one of `cloud`, `local` or `raw-tracks`.",
    )
    enable_prejoin_ui: Optional[bool] = Field(
        default=None,
        description="If `true`, the user will see the prejoin UI before joining the room.",
    )
    start_cloud_recording: Optional[bool] = Field(
        default=None,
        description="Start cloud recording when the user joins the room. This can be used to always record and archive meetings, for example in a customer support context.",
    )


class DailyMeetingTokenParams(BaseModel):
    """Parameters for creating a Daily meeting token.

    Refer to the Daily API documentation for more information:
    https://docs.daily.co/reference/rest-api/meeting-tokens/create-meeting-token#body-params
    """

    properties: DailyMeetingTokenProperties = Field(default_factory=DailyMeetingTokenProperties)


class DailyMeetingSessionParticipant(BaseModel):
    """Represents a participant in a Daily meeting session."""

    user_id: Optional[str] = None
    joined_at: Optional[int] = None
    duration: Optional[int] = None


class DailyMeetingSession(BaseModel):
    """Represents a Daily meeting session returned by the API.

    Attributes:
        id: Unique session identifier.
        room: The room name where the session took place.
        started_at: Timestamp of session start in ISO 8601 format.
        duration: Session duration in seconds.
        participants: List of participant details.
    """

    id: str
    room: str
    started_at: str
    duration: int
    participants: List[DailyMeetingSessionParticipant] = Field(default_factory=list)


class DailyMeetingSessionList(BaseModel):
    """Represents a list of Daily meeting sessions returned by the API.

    Attributes:
        total_count: Total number of sessions.
        data: List of DailyMeetingSession objects.
    """

    total_count: int
    data: List[DailyMeetingSession]


class CustomDailyRESTHelper:
    """Extends DailyRESTHelper with custom functionality."""

    def __init__(self, daily_rest_helper: DailyRESTHelper):
        self.daily_rest_helper = daily_rest_helper
        self.daily_api_key = daily_rest_helper.daily_api_key
        self.daily_api_url = daily_rest_helper.daily_api_url
        self.aiohttp_session = daily_rest_helper.aiohttp_session

    async def get_participants(self, room_url: str) -> List[DailyMeetingSessionParticipant]:
        """Get the list of participants in a Daily room.

        Args:
            room_url: Daily room URL.

        Raises:
            Exception: If retrieving participants fails or room URL is missing.
        """
        logger.debug(f"get_participants - Input room_url: {room_url}")

        if not room_url:
            raise Exception("No Daily room specified.")

        room_name = self.daily_rest_helper.get_name_from_url(room_url)
        headers = {"Authorization": f"Bearer {self.daily_api_key}"}

        # Use the NEW /presence endpoint
        async with self.aiohttp_session.get(
            f"{self.daily_api_url}/rooms/{room_name}/presence", headers=headers  # Corrected endpoint to /presence
        ) as r:
            if r.status != 200:
                text = await r.text()
                raise Exception(
                    f"Failed to get room presence for room {room_name} (status: {r.status}): {text}"  # Updated error message
                )

            data = await r.json()

        try:
            # The /presence endpoint returns a different response structure.
            # It has "total_count" and "data" fields, but "data" is a list of participant presence objects.
            # We are interested in whether total_count > 0, which indicates active participants.
            presence_data = data  # /presence returns different data structure
            participant_count = presence_data.get("total_count", 0)  # Get total_count from presence data
            if participant_count > 0:
                logger.debug(
                    f"get_participants - Found {participant_count} participants in room {room_name} using /presence endpoint."
                )
                return [DailyMeetingSessionParticipant()] * participant_count  # Return a list of dummy DailyMeetingSessionParticipant objects, length = participant_count for is_active check
            else:
                logger.debug(
                    f"get_participants - No participants found in room {room_name} using /presence endpoint."
                )
                return []  # No participants found
        except Exception as e:
            raise Exception(f"Error processing /presence response: {e}")  # Updated exception message

    async def is_room_active(self, room_url: str) -> bool:
        """Check if a room is considered active based on current participants.

        Args:
            room_url: The URL of the room to check.

        Returns:
            bool: True if the room has participants, False otherwise.

        Raises:
            Exception: If retrieving room information fails.
        """
        logger.debug(f"is_room_active - Checking room URL: {room_url}")  # Added log
        participants = await self.get_participants(room_url)
        logger.debug(f"is_room_active - Participants for room {room_url}: {participants}")  # Added log

        if participants:
            logger.debug(f"Room {room_url} is active because participants are present.")
            return True
        logger.debug(f"Room {room_url} is inactive because no participants are present.")
        return False

    async def create_room(self, params: DailyRoomParams) -> DailyRoomObject:
        """Create a new Daily room."""
        return await self.daily_rest_helper.create_room(params)

    async def get_recordings(self, room_name: Optional[str] = None, limit: int = 100) -> List[Dict]:
        """Get recordings for a room or all recordings."""
        headers = {"Authorization": f"Bearer {self.daily_api_key}"}

        params = {"limit": limit}
        if room_name:
            params["room_name"] = room_name

        async with self.aiohttp_session.get(
            f"{self.daily_api_url}/recordings", headers=headers, params=params
        ) as r:
            if r.status != 200:
                text = await r.text()
                raise Exception(f"Failed to get recordings (status: {r.status}): {text}")

            data = await r.json()
            return data.get("data", [])

    async def get_recording_by_id(self, recording_id: str) -> Optional[Dict]:
        """Get a specific recording by ID."""
        headers = {"Authorization": f"Bearer {self.daily_api_key}"}

        async with self.aiohttp_session.get(
            f"{self.daily_api_url}/recordings/{recording_id}", headers=headers
        ) as r:
            if r.status == 404:
                return None
            if r.status != 200:
                text = await r.text()
                raise Exception(f"Failed to get recording (status: {r.status}): {text}")

            data = await r.json()
            return data

    async def get_token(
        self,
        room_url: str,
        expiry_time: float = 60 * 60,
        owner: bool = True,
        params: Optional[DailyMeetingTokenParams] = None,
    ) -> str:
        """Generate a meeting token for a Daily room."""
        if not room_url:
            raise Exception(
                "No Daily room specified. You must specify a Daily room in order a token to be generated."
            )

        expiration: int = int(time.time() + expiry_time)

        room_name = self.daily_rest_helper.get_name_from_url(room_url)

        headers = {"Authorization": f"Bearer {self.daily_api_key}"}

        # Construct parameters directly as a dictionary
        token_params = {
            "properties": {
                "room_name": room_name,
                "is_owner": owner,
                "exp": expiration,
            }
        }

        # Update token_params with values from provided params if any
        if params and params.properties:
            token_params["properties"].update(params.properties.model_dump(exclude_none=True))

        async with self.aiohttp_session.post(
            f"{self.daily_api_url}/meeting-tokens", headers=headers, json=token_params
        ) as r:
            if r.status != 200:
                text = await r.text()
                raise Exception(f"Failed to create meeting token (status: {r.status}): {text}")

            data = await r.json()

        return data["token"]

    async def delete_room_by_url(self, room_url: str) -> bool:
        """Delete a room using its URL."""
        return await self.daily_rest_helper.delete_room_by_url(room_url)

    async def get_room_from_url(self, room_url: str) -> DailyRoomObject:
        """Get room details from a Daily room URL."""
        room_name = self.daily_rest_helper.get_name_from_url(room_url)
        return await self.daily_rest_helper._get_room_from_name(room_name)

    async def get_rooms(self, limit: int = 100, ending_before: Optional[str] = None, starting_after: Optional[str] = None) -> List[DailyRoomObject]:
        """Get a list of rooms from the Daily API.

        Args:
            limit: The maximum number of rooms to return (default: 100).
            ending_before: Return rooms created before the room ID specified by this parameter.
            starting_after: Return rooms created after the room ID specified by this parameter.

        Returns:
            List[DailyRoomObject]: A list of DailyRoomObject instances.

        Raises:
            Exception: If retrieving rooms fails.
        """
        headers = {"Authorization": f"Bearer {self.daily_api_key}"}
        params = {"limit": limit}
        if ending_before:
            params["ending_before"] = params["ending_before"]
        if starting_after:
            params["starting_after"] = starting_after

        async with self.aiohttp_session.get(  # Use session from class
            f"{self.daily_api_url}/rooms", headers=headers, params=params
        ) as r:
            if r.status != 200:
                text = await r.text()
                raise Exception(f"Failed to get rooms (status: {r.status}): {text}")

            data = await r.json()

        try:
            # Assuming the response has a 'data' field containing a list of rooms
            rooms = [DailyRoomObject(**room_data) for room_data in data.get("data", [])]
            return rooms
        except (KeyError, TypeError, ValueError) as e:
            raise Exception(f"Invalid response format: {e}")