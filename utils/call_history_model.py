from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum

class CallStatus(str, Enum):
    ACTIVE = "active"
    COMPLETED = "completed"
    FAILED = "failed"
    ABANDONED = "abandoned"

class ParticipantType(str, Enum):
    USER = "user"
    AGENT = "agent"
    BOT = "bot"

class CallHistory(BaseModel):
    """Enhanced call history model with comprehensive tracking"""
    id: Optional[str] = None
    call_id: str = Field(..., description="Unique identifier for the call session")
    agent_id: Optional[str] = None
    user_id: Optional[str] = None
    room_url: str = Field(..., description="Daily.co room URL")
    
    # Call Timing
    start_time: datetime = Field(..., description="Call start timestamp")
    end_time: Optional[datetime] = None
    call_duration_seconds: Optional[int] = None
    
    # Call Content
    full_transcript: Optional[str] = None
    call_recording_url: Optional[str] = None
    
    # Call Quality & Metrics
    call_status: CallStatus = Field(default=CallStatus.ACTIVE)
    call_quality_score: Optional[float] = Field(None, ge=1.0, le=5.0, description="Technical quality score (1.00-5.00)")
    user_satisfaction_rating: Optional[int] = Field(None, ge=1, le=5, description="User satisfaction rating (1-5 stars)")
    
    # Service Configuration Used
    stt_service: Optional[str] = None
    llm_service: Optional[str] = None
    tts_service: Optional[str] = None
    stt_config: Optional[Dict[str, Any]] = Field(default_factory=dict)
    llm_config: Optional[Dict[str, Any]] = Field(default_factory=dict)
    tts_config: Optional[Dict[str, Any]] = Field(default_factory=dict)
    
    # Usage Metrics
    total_tokens_used: int = Field(default=0, description="Total LLM tokens consumed")
    stt_duration_seconds: int = Field(default=0, description="Total STT processing time")
    tts_characters_generated: int = Field(default=0, description="Total TTS characters generated")
    estimated_cost: float = Field(default=0.0, description="Estimated cost in USD")
    
    # Technical Metrics
    average_response_time_ms: Optional[int] = Field(None, description="Average response time in milliseconds")
    error_count: int = Field(default=0, description="Number of errors during call")
    interruption_count: int = Field(default=0, description="Number of user interruptions")
    
    # Additional Data
    user_agent: Optional[str] = None
    ip_address: Optional[str] = None
    country_code: Optional[str] = None
    language_detected: Optional[str] = None
    conversation_summary: Optional[str] = None
    tags: List[str] = Field(default_factory=list)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    # Timestamps
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        use_enum_values = True

class CallParticipant(BaseModel):
    """Model for tracking call participants"""
    id: Optional[str] = None
    call_id: str = Field(..., description="Reference to call_history.id")
    participant_id: str = Field(..., description="Daily.co participant ID")
    participant_type: ParticipantType = Field(default=ParticipantType.USER)
    participant_name: Optional[str] = None
    joined_at: datetime = Field(..., description="When participant joined")
    left_at: Optional[datetime] = None
    duration_seconds: Optional[int] = None
    created_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        use_enum_values = True

class CallEvent(BaseModel):
    """Model for tracking call events"""
    id: Optional[str] = None
    call_id: str = Field(..., description="Reference to call_history.id")
    event_type: str = Field(..., description="Type of event (e.g., 'participant_joined', 'transcript_received')")
    event_data: Dict[str, Any] = Field(default_factory=dict, description="Event-specific data")
    timestamp: datetime = Field(..., description="When the event occurred")
    created_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class CallAnalyticsSummary(BaseModel):
    """Model for call analytics summary data"""
    call_date: str
    agent_id: Optional[str] = None
    total_calls: int = 0
    completed_calls: int = 0
    failed_calls: int = 0
    abandoned_calls: int = 0
    avg_duration_seconds: Optional[float] = None
    avg_quality_score: Optional[float] = None
    avg_satisfaction_rating: Optional[float] = None
    total_tokens: int = 0
    total_cost: float = 0.0
    avg_response_time_ms: Optional[float] = None

    class Config:
        from_attributes = True

class ActiveCall(BaseModel):
    """Model for active call monitoring"""
    id: str
    call_id: str
    agent_id: Optional[str] = None
    agent_name: Optional[str] = None
    user_id: Optional[str] = None
    room_url: str
    start_time: datetime
    current_duration_seconds: int
    call_status: CallStatus
    participant_count: Optional[int] = None

    class Config:
        from_attributes = True
        use_enum_values = True

class CallHistoryFilter(BaseModel):
    """Model for filtering call history"""
    agent_id: Optional[str] = None
    user_id: Optional[str] = None
    call_status: Optional[CallStatus] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    min_duration: Optional[int] = None
    max_duration: Optional[int] = None
    search_query: Optional[str] = None
    tags: Optional[List[str]] = None
    page: int = Field(default=1, ge=1)
    page_size: int = Field(default=20, ge=1, le=100)
    sort_by: str = Field(default="start_time")
    sort_order: str = Field(default="desc", regex="^(asc|desc)$")

    class Config:
        use_enum_values = True

class CallHistoryResponse(BaseModel):
    """Response model for paginated call history"""
    calls: List[CallHistory]
    total_count: int
    page: int
    page_size: int
    total_pages: int
    has_next: bool
    has_previous: bool

class CallRating(BaseModel):
    """Model for submitting call ratings"""
    call_id: str
    user_satisfaction_rating: int = Field(..., ge=1, le=5)
    feedback_text: Optional[str] = None
    quality_issues: Optional[List[str]] = None

class CallMetricsUpdate(BaseModel):
    """Model for updating call metrics during or after a call"""
    call_id: str
    total_tokens_used: Optional[int] = None
    stt_duration_seconds: Optional[int] = None
    tts_characters_generated: Optional[int] = None
    estimated_cost: Optional[float] = None
    average_response_time_ms: Optional[int] = None
    error_count: Optional[int] = None
    interruption_count: Optional[int] = None
    call_quality_score: Optional[float] = Field(None, ge=1.0, le=5.0)

# Legacy model for backward compatibility
class CallLogEntry(CallHistory):
    """Legacy model - use CallHistory instead"""
    pass
