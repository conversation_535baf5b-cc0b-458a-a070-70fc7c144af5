# bot_framework.py (Complete, with Subprocess Management - Final, Corrected Version)
import asyncio
import time
from loguru import logger
from datetime import datetime, timezone
from typing import Optional, List
from utils.call_log_service import CallLogService
from abc import ABC, abstractmethod
from pipecat.pipeline.task import PipelineParams, PipelineTask
from pipecat.pipeline.runner import PipelineRunner
from pipecat.processors.frameworks.rtvi import RTVIConfig, RTVIProcessor
from pipecat.services.deepgram import DeepgramSTTService
from deepgram import LiveTranscriptionEvents, LiveResultResponse
from .services import ServiceRegistry
from .transports import TransportFactory
from .events import EventFramework
from .pipelines import PipelineBuilder
from pipecat.audio.vad.vad_analyzer import VADParams
import contextvars  # Import contextvars
import os # Import os
from supabase import create_client, Client
from utils.call_log_service import CallLogService, CallLogEntry
from utils.call_history_service import CallHistoryService
from utils.call_history_model import CallHistory, CallStatus
from utils.tts_tracker import TTSTracker, TranscriptCollector



class BaseBot(ABC):
    def __init__(self, config, agent_config=None, stt_service=None, stt_config=None, llm_service=None, llm_config=None, tts_service=None, tts_config=None, vad_config=None, call_log_service=None, room_url=None, app=None): # Add app
        super().__init__()
        self.config = config
        self.agent_config = agent_config
        self.stt_service = stt_service
        self.stt_config = stt_config
        self.llm_service = llm_service
        self.llm_config = llm_config
        self.tts_service = tts_service
        self.tts_config = tts_config
        self.services = ServiceRegistry(config)  # Initialize ServiceRegistry
        self.runner = None
        self.transport = None
        self.task = None
        self.rtvi = None
        self.context = None
        self.context_aggregator = None
        self.pipeline_builder = None  # Initialize pipeline_builder
        self.vad_config = vad_config
        self.last_message_time = time.time()
        self.call_log_service: Optional[CallLogService] = call_log_service
        self.call_history_service: Optional[CallHistoryService] = None
        self.call_log_id: Optional[str] = None
        self.call_history_id: Optional[str] = None
        self.call_history_call_id: Optional[str] = None
        self.transcript: str = ""
        self.agent_transcript: str = ""  # Track agent speech separately
        self.user_transcript: str = ""   # Track user speech separately
        self.transcript_collector = TranscriptCollector()  # Enhanced transcript management
        self.tts_tracker: Optional[TTSTracker] = None
        self.room_url = room_url # Store room_url
        self._cleaned_up = False  # Add a flag to track cleanup status
        self.shutdown_event = asyncio.Event() # Create an asyncio Event


    async def setup_services(self):
        """Initialize required services."""
        rtvi_config = RTVIConfig(config=[])
        self.rtvi = RTVIProcessor(config=rtvi_config)

        @self.rtvi.event_handler("on_client_ready")
        async def on_client_ready(rtvi):
            await rtvi.set_bot_ready()
            logger.info("RTVI client ready event handled in BaseBot.")

        self.services.initialize_services(self.config, self.agent_config)
        await self._setup_services_impl()
        await self._setup_call_logging() # Call the new method
        self._setup_transcript_tracking() # Setup transcript tracking

        if isinstance(self.services.stt, DeepgramSTTService):
            stt_service = self.services.stt
            connection = stt_service._connection
            if connection:
                connection.on(LiveTranscriptionEvents.Transcript, self._on_deepgram_transcript)
                logger.info("Registered event handler for Deepgram LiveTranscriptionEvents.Transcript")
            else:
                logger.warning("DeepgramSTTService connection object not found, cannot register transcript event handler.")
        else:
            logger.warning("STT Service is not DeepgramSTTService, skipping Deepgram transcript event handler registration.")

    async def _setup_call_logging(self):
        """Initialize call logging if Supabase credentials are available."""
        supabase_url = os.environ.get("SUPABASE_URL")
        supabase_key = os.environ.get("SUPABASE_KEY")
        if supabase_url and supabase_key:
            supabase_client: Client = create_client(supabase_url, supabase_key)
            self.call_log_service = CallLogService(supabase_client)
            self.call_history_service = CallHistoryService(supabase_client)
            logger.info("CallLogService and CallHistoryService initialized in bot process.")
        else:
            logger.warning("Supabase credentials not found in environment, call logging disabled.")

    def _setup_transcript_tracking(self):
        """Initialize transcript tracking components."""
        # Create TTS tracker with callback to transcript collector
        self.tts_tracker = TTSTracker(callback=self.transcript_collector.add_agent_speech)
        logger.info("TTS tracker initialized for comprehensive transcript collection.")

    @abstractmethod
    async def _setup_services_impl(self):
        """Implementation-specific service setup."""
        pass



    @abstractmethod
    async def _create_transport(self, factory: TransportFactory, url: str, token: str, vad_config: VADParams = None):
        """Implementation-specific transport creation."""
        pass


    @abstractmethod
    async def _handle_first_participant(self):
        """Implementation-specific first participant handling. To be overridden by subclasses."""
        pass

    async def setup_transport(self, url: str, token: str):
        """Transport setup with event handlers."""
        transport_factory = TransportFactory(self.config)
        self.transport = await self._create_transport(transport_factory, url, token, self.vad_config)

        event_framework = EventFramework(self.transport)
        await event_framework.register_default_handlers(self.cleanup)

        @self.transport.event_handler("on_participant_left")
        async def on_participant_left(transport, participant, reason):
            local_participant = transport._client.participants().get("local")
            if local_participant and participant["id"] != local_participant["id"]:
                logger.info(f"Local participant still in room, but remote participant {participant['id']} left.  Triggering cleanup.")
                await self.cleanup()
            else:
                logger.info(f"Local participant left or participant left was local.  No cleanup triggered based on participant leave event.")
            await self.runner.stop_when_done()

        @self.transport.event_handler("on_first_participant_joined")
        async def on_first_participant_joined(transport, participant, room_url=url): # Pass room_url as a default argument
            logger.info(f"on_first_participant_joined called! Participant: {participant}, Room URL: {room_url}") # Log the room_url
            self.last_message_time = time.time()
            await transport.capture_participant_transcription(participant["id"])

            # --- Create CallLogEntry and CallHistory HERE ---
            if self.call_log_service:
                now = datetime.now(timezone.utc)
                start_time = now.strftime("%Y-%m-%dT%H:%M:%S%z")
                call_log_entry = CallLogEntry(
                    agent_id=self.agent_config.get("id") if self.agent_config else None,
                    room_url=room_url,  # Use the passed room_url
                    start_time=start_time
                )
                created_log_entry = await self.call_log_service.create_call_log_entry(call_log_entry)
                self.call_log_id = created_log_entry.id
                logger.info(f"Call log entry created in Supabase with ID: {self.call_log_id}")

            # Create enhanced call history entry
            if self.call_history_service:
                import uuid
                call_id = str(uuid.uuid4())
                call_history = CallHistory(
                    call_id=call_id,
                    agent_id=self.agent_config.get("id") if self.agent_config else None,
                    room_url=room_url,
                    start_time=datetime.now(timezone.utc),
                    call_status=CallStatus.ACTIVE,
                    stt_service=getattr(self.services.stt, 'service_name', None) if hasattr(self, 'services') and self.services.stt else None,
                    llm_service=getattr(self.services.llm, 'service_name', None) if hasattr(self, 'services') and self.services.llm else None,
                    tts_service=getattr(self.services.tts, 'service_name', None) if hasattr(self, 'services') and self.services.tts else None,
                    total_tokens_used=0,
                    stt_duration_seconds=0,
                    tts_characters_generated=0,
                    estimated_cost=0.0,
                    error_count=0,
                    interruption_count=0,
                    tags=[]
                )
                created_history = await self.call_history_service.create_call(call_history)
                self.call_history_id = created_history.id
                self.call_history_call_id = call_id
                logger.info(f"Call history entry created in Supabase with ID: {self.call_history_id}, call_id: {call_id}")
            # --- End CallLogEntry and CallHistory Creation ---

            await self._handle_first_participant()
            logger.info(f"First participant {participant['id']} joined, transcription started.")


        @self.rtvi.event_handler("on_client_ready")
        async def on_client_ready_rtvi(rtvi):
            await rtvi.set_bot_ready()
            logger.info("RTVI client ready event handled in setup_transport.")

        await self._setup_transport_impl()




    async def _setup_transport_impl(self):
        """Optional implementation-specific transport setup."""
        pass


    def create_pipeline(self):
        """Pipeline construction, now using the wrapped transport."""
        # Create the PipelineBuilder *after* the transport is wrapped
        self.pipeline_builder = PipelineBuilder(
            self.transport,  # Pass the *wrapped* transport
            self.services.stt,
            self.services.tts,
            self.services.llm,
            context=self.context,
            tts_tracker=self.tts_tracker,  # Pass TTS tracker for transcript collection
        )
        pipeline = self.pipeline_builder.add_rtvi(self.rtvi).build()

        self.task = PipelineTask(
            pipeline,
            PipelineParams(
                allow_interruptions=True,
                enable_metrics=True,
                enable_usage_metrics=True,
                observers=[self.rtvi.observer()],
            ),
        )
        self.runner = PipelineRunner() # NO SubprocessManager here!
        asyncio.create_task(self._create_pipeline_impl())  # Run subclass-specific pipeline setup

    @abstractmethod
    async def _create_pipeline_impl(self):
        """Implementation-specific pipeline setup (subclasses override)."""
        self.services.llm.set_callback(self.update_last_message_time)
        self.services.stt.set_callback(self.update_last_message_time)
        pass


    async def start(self):
        """Start the bot's main task."""
        if self.runner and self.task:
            logger.info("Starting pipeline runner...")
            await self.runner.run(self.task) # Run the pipeline task
            logger.info("Pipeline runner finished.")
        else:
            error_message = "Bot not properly initialized. Call setup methods first (setup_services, setup_transport, create_pipeline)."
            logger.error(error_message)
            raise RuntimeError(error_message)

    async def stop(self):
        """Gracefully stop the bot."""
        if self.runner:
            logger.info("Stopping pipeline runner - cancellation...")
            await self.runner.cancel()
        self.shutdown_event.set()  # Signal any waiting tasks to stop



    async def cleanup(self):
        """Clean up resources. Called on errors or participant leave."""
        logger.info("Starting cleanup process...")

        # --- Call Logging Updates ---
        now = datetime.now(timezone.utc)
        end_time = now.strftime("%Y-%m-%dT%H:%M:%S%z")  # Correct format

        # Update legacy call log
        if self.call_log_id and self.call_log_service:
            logger.info("Updating call log entry with end time, duration, and transcript...")

            # Calculate duration
            start_entry = await self.call_log_service.get_call_log_entry(self.call_log_id)
            if start_entry and start_entry.start_time:
                start_datetime = datetime.fromisoformat(start_entry.start_time)
                # Ensure end_datetime is timezone-aware
                end_datetime = datetime.fromisoformat(end_time)

                call_duration = int((end_datetime - start_datetime).total_seconds())
            else:
                call_duration = None # Handle case where start_time is missing

            updates = {
                "end_time": end_time,
                "call_duration_seconds": call_duration,
                "full_transcript": self.transcript,
                "status": "completed"  # Or "error" if there was an error
            }
            await self.call_log_service.update_call_log_entry(self.call_log_id, updates)
            logger.info(f"Call log entry updated: ID={self.call_log_id}, duration={call_duration}, status=completed")
        else:
            logger.warning("CallLogService not initialized or no call_log_id, skipping call log update.")

        # Update enhanced call history
        if self.call_history_call_id and self.call_history_service:
            logger.info("Updating call history entry with comprehensive data...")

            # Get the original call history entry to calculate duration
            call_history = await self.call_history_service.get_call(self.call_history_call_id)
            call_duration = None
            if call_history and call_history.start_time:
                call_duration = int((now - call_history.start_time).total_seconds())

            # Prepare comprehensive transcript with speaker labels
            full_transcript = self.transcript_collector.get_formatted_transcript()
            if not full_transcript and self.transcript:
                # Fallback to combined transcript if transcript collector is empty
                full_transcript = self.transcript.strip()

            history_updates = {
                "end_time": now,
                "call_duration_seconds": call_duration,
                "full_transcript": full_transcript,
                "call_status": CallStatus.COMPLETED.value,
                "stt_duration_seconds": call_duration or 0,
                "tts_characters_generated": len(self.transcript_collector.get_agent_transcript()),
                # Add more metrics as needed
            }

            await self.call_history_service.update_call(self.call_history_call_id, history_updates)
            logger.info(f"Call history entry updated: call_id={self.call_history_call_id}, duration={call_duration}, status=completed")
        else:
            logger.warning("CallHistoryService not initialized or no call_history_call_id, skipping call history update.")
        # --- End Call Logging Updates ---

        if self.runner:
            logger.info("Stopping pipeline runner...")
            await self.runner.stop_when_done() # Stop the pipeline runner
        if self.transport:
            logger.info("Leaving Daily room...")
            await self.transport.leave() # Leave the Daily room
        logger.info("Cleanup process completed.")




    async def _on_deepgram_transcript(self, *args, **kwargs):
        """Event handler for Deepgram transcript events."""
        if "result" not in kwargs:
            logger.warning("'_on_deepgram_transcript' event received without 'result' in kwargs.")
            return

        result_data = kwargs["result"]

        if not hasattr(result_data, 'channel') or not hasattr(result_data.channel, 'alternatives'):
            logger.warning("'_on_deepgram_transcript' event 'result' is missing expected structure.")
            return

        if len(result_data.channel.alternatives) == 0:
            return

        transcript_chunk = result_data.channel.alternatives[0].transcript
        if transcript_chunk:
            self._process_stt_transcript(transcript_chunk)
            self.update_last_message_time()

    def _process_stt_transcript(self, transcript_chunk: str):
        """Processes STT transcript chunks (user speech)."""
        if transcript_chunk:
            self.transcript += transcript_chunk + " "
            self.user_transcript += transcript_chunk + " "
            # Add to transcript collector for enhanced tracking
            self.transcript_collector.add_user_speech(transcript_chunk)
            logger.debug(f"Received STT transcript chunk (user): '{transcript_chunk.strip()}', accumulated transcript length: {len(self.transcript)}")

    def _process_tts_text(self, text: str):
        """Processes TTS text (agent speech)."""
        if text:
            self.agent_transcript += text + " "
            # Add to transcript collector for enhanced tracking
            self.transcript_collector.add_agent_speech(text)
            logger.debug(f"Agent TTS text: '{text.strip()}', accumulated agent transcript length: {len(self.agent_transcript)}")

    def update_last_message_time(self, *args, **kwargs):
        """Updates the last message time."""
        self.last_message_time = time.time()