'use client';

import { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  Di<PERSON>Title,
  DialogClose
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  X,
  Phone,
  Clock,
  Star,
  Download,
  Play,
  Pause,
  Volume2,
  MessageSquare,
  DollarSign,
  Activity,
  AlertCircle,
  CheckCircle,
  XCircle,
  Settings
} from 'lucide-react';

interface CallHistory {
  id: string;
  call_id: string;
  agent_id?: string;
  user_id?: string;
  room_url: string;
  start_time: string;
  end_time?: string;
  call_duration_seconds?: number;
  full_transcript?: string;
  call_recording_url?: string;
  call_status: 'active' | 'completed' | 'failed' | 'abandoned';
  call_quality_score?: number;
  user_satisfaction_rating?: number;
  stt_service?: string;
  llm_service?: string;
  tts_service?: string;
  stt_config?: Record<string, unknown>;
  llm_config?: Record<string, unknown>;
  tts_config?: Record<string, unknown>;
  total_tokens_used: number;
  stt_duration_seconds: number;
  tts_characters_generated: number;
  estimated_cost: number;
  average_response_time_ms?: number;
  error_count: number;
  interruption_count: number;
  user_agent?: string;
  ip_address?: string;
  country_code?: string;
  language_detected?: string;
  conversation_summary?: string;
  tags: string[];
  metadata?: Record<string, unknown>;
  created_at: string;
  updated_at?: string;
}

interface CallDetailsModalProps {
  call: CallHistory | null;
  isOpen: boolean;
  onClose: () => void;
}

export default function CallDetailsModal({ call, isOpen, onClose }: CallDetailsModalProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime] = useState(0);
  const [duration] = useState(0);

  if (!call) return null;

  const formatDuration = (seconds?: number) => {
    if (!seconds) return 'N/A';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { color: 'bg-blue-500', icon: Phone, text: 'Active' },
      completed: { color: 'bg-green-500', icon: CheckCircle, text: 'Completed' },
      failed: { color: 'bg-red-500', icon: XCircle, text: 'Failed' },
      abandoned: { color: 'bg-yellow-500', icon: AlertCircle, text: 'Abandoned' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.failed;
    const Icon = config.icon;

    return (
      <Badge className={`${config.color} text-white`}>
        <Icon className="w-3 h-3 mr-1" />
        {config.text}
      </Badge>
    );
  };

  const renderStars = (rating?: number) => {
    if (!rating) return <span className="text-gray-400">No rating</span>;

    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`w-4 h-4 ${
              star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
            }`}
          />
        ))}
        <span className="ml-1 text-sm text-gray-600">({rating})</span>
      </div>
    );
  };

  const handlePlayPause = () => {
    setIsPlaying(!isPlaying);
    // Audio playback logic would go here
  };

  const downloadTranscript = () => {
    if (!call.full_transcript) return;

    const blob = new Blob([call.full_transcript], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `call-transcript-${call.call_id}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex justify-between items-start">
            <div>
              <DialogTitle className="text-xl font-bold">
                Call Details - {call.call_id.slice(0, 8)}...
              </DialogTitle>
              <div className="flex items-center space-x-2 mt-2">
                {getStatusBadge(call.call_status)}
                <span className="text-sm text-gray-600">
                  {formatDate(call.start_time)}
                </span>
              </div>
            </div>
            <DialogClose asChild>
              <Button variant="ghost" size="sm">
                <X className="w-4 h-4" />
              </Button>
            </DialogClose>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Call Overview */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center">
                  <Clock className="w-6 h-6 text-blue-500 mr-3" />
                  <div>
                    <p className="text-sm text-gray-600">Duration</p>
                    <p className="text-lg font-semibold">
                      {formatDuration(call.call_duration_seconds)}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center">
                  <DollarSign className="w-6 h-6 text-green-500 mr-3" />
                  <div>
                    <p className="text-sm text-gray-600">Cost</p>
                    <p className="text-lg font-semibold">
                      ${call.estimated_cost.toFixed(4)}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center">
                  <Star className="w-6 h-6 text-yellow-500 mr-3" />
                  <div>
                    <p className="text-sm text-gray-600">Rating</p>
                    <div className="mt-1">
                      {renderStars(call.user_satisfaction_rating)}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Call Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Phone className="w-5 h-5 mr-2" />
                Call Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-600">Call ID</label>
                  <p className="text-sm font-mono bg-gray-100 p-2 rounded">
                    {call.call_id}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Agent ID</label>
                  <p className="text-sm">{call.agent_id || 'N/A'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">User ID</label>
                  <p className="text-sm">{call.user_id || 'Anonymous'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Language</label>
                  <p className="text-sm">{call.language_detected || 'N/A'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Country</label>
                  <p className="text-sm">{call.country_code || 'N/A'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">IP Address</label>
                  <p className="text-sm font-mono">{call.ip_address || 'N/A'}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Technical Metrics */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Activity className="w-5 h-5 mr-2" />
                Technical Metrics
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-600">Tokens Used</label>
                  <p className="text-lg font-semibold">{call.total_tokens_used.toLocaleString()}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Response Time</label>
                  <p className="text-lg font-semibold">
                    {call.average_response_time_ms ? `${call.average_response_time_ms}ms` : 'N/A'}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Quality Score</label>
                  <p className="text-lg font-semibold">
                    {call.call_quality_score ? `${call.call_quality_score}/5.0` : 'N/A'}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Errors</label>
                  <p className="text-lg font-semibold text-red-600">{call.error_count}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Interruptions</label>
                  <p className="text-lg font-semibold">{call.interruption_count}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">TTS Characters</label>
                  <p className="text-lg font-semibold">{call.tts_characters_generated.toLocaleString()}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Service Configuration */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Settings className="w-5 h-5 mr-2" />
                Service Configuration
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-600">STT Service</label>
                  <p className="text-sm">{call.stt_service || 'N/A'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">LLM Service</label>
                  <p className="text-sm">{call.llm_service || 'N/A'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">TTS Service</label>
                  <p className="text-sm">{call.tts_service || 'N/A'}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Audio Recording */}
          {call.call_recording_url && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Volume2 className="w-5 h-5 mr-2" />
                  Call Recording
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handlePlayPause}
                  >
                    {isPlaying ? (
                      <Pause className="w-4 h-4 mr-2" />
                    ) : (
                      <Play className="w-4 h-4 mr-2" />
                    )}
                    {isPlaying ? 'Pause' : 'Play'}
                  </Button>
                  <div className="flex-1 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-500 h-2 rounded-full"
                      style={{ width: `${duration ? (currentTime / duration) * 100 : 0}%` }}
                    ></div>
                  </div>
                  <span className="text-sm text-gray-600">
                    {formatDuration(currentTime)} / {formatDuration(duration)}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => window.open(call.call_recording_url, '_blank')}
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Download
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Transcript */}
          {call.full_transcript && (
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle className="flex items-center">
                    <MessageSquare className="w-5 h-5 mr-2" />
                    Transcript
                  </CardTitle>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={downloadTranscript}
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Download
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-50 p-4 rounded-lg max-h-64 overflow-y-auto">
                  <pre className="whitespace-pre-wrap text-sm text-gray-700">
                    {call.full_transcript}
                  </pre>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Conversation Summary */}
          {call.conversation_summary && (
            <Card>
              <CardHeader>
                <CardTitle>Conversation Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700">{call.conversation_summary}</p>
              </CardContent>
            </Card>
          )}

          {/* Tags */}
          {call.tags && call.tags.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Tags</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {call.tags.map((tag, index) => (
                    <Badge key={index} variant="secondary">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
