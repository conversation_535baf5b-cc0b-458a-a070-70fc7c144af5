'use client';

import { useState, useEffect, useCallback } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Search,
  Download,
  Phone,
  Clock,
  Calendar,
  ChevronLeft,
  ChevronRight,
  Star,
  AlertCircle,
  CheckCircle,
  XCircle,
  Video,
  VideoOff
} from 'lucide-react';
import Link from 'next/link';
import CallDetailsModal from '@/components/CallDetailsModal';

// API configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:7860';

interface CallHistory {
  id: string;
  call_id: string;
  agent_id?: string;
  user_id?: string;
  room_url: string;
  start_time: string;
  end_time?: string;
  call_duration_seconds?: number;
  full_transcript?: string;
  call_recording_url?: string;
  call_status: 'active' | 'completed' | 'failed' | 'abandoned';
  call_quality_score?: number;
  user_satisfaction_rating?: number;
  stt_service?: string;
  llm_service?: string;
  tts_service?: string;
  stt_config?: Record<string, unknown>;
  llm_config?: Record<string, unknown>;
  tts_config?: Record<string, unknown>;
  total_tokens_used: number;
  stt_duration_seconds: number;
  tts_characters_generated: number;
  estimated_cost: number;
  average_response_time_ms?: number;
  error_count: number;
  interruption_count: number;
  user_agent?: string;
  ip_address?: string;
  country_code?: string;
  language_detected?: string;
  conversation_summary?: string;
  tags: string[];
  metadata?: Record<string, unknown>;
  created_at: string;
  updated_at?: string;
}

interface CallHistoryResponse {
  calls: CallHistory[];
  total_count: number;
  page: number;
  page_size: number;
  total_pages: number;
  has_next: boolean;
  has_previous: boolean;
}

export default function CallHistoryPage() {
  const [calls, setCalls] = useState<CallHistory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [selectedCall, setSelectedCall] = useState<CallHistory | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [filters, setFilters] = useState({
    agent_id: '',
    user_id: '',
    call_status: '',
    start_date: '',
    end_date: ''
  });

  const fetchCalls = useCallback(async (page = 1) => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        page_size: '20',
        sort_by: 'start_time',
        sort_order: 'desc'
      });

      if (searchQuery) params.append('search_query', searchQuery);
      if (filters.agent_id) params.append('agent_id', filters.agent_id);
      if (filters.user_id) params.append('user_id', filters.user_id);
      if (filters.call_status) params.append('call_status', filters.call_status);
      if (filters.start_date) params.append('start_date', filters.start_date);
      if (filters.end_date) params.append('end_date', filters.end_date);

      const response = await fetch(`${API_BASE_URL}/api/calls/?${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch calls');
      }

      const data: CallHistoryResponse = await response.json();
      setCalls(data.calls);
      setCurrentPage(data.page);
      setTotalPages(data.total_pages);
      setTotalCount(data.total_count);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  }, [searchQuery, filters]);

  useEffect(() => {
    fetchCalls(currentPage);
  }, [currentPage, searchQuery, filters, fetchCalls]);

  const formatDuration = (seconds?: number) => {
    if (!seconds) return 'N/A';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { color: 'bg-blue-500', icon: Phone, text: 'Active' },
      completed: { color: 'bg-green-500', icon: CheckCircle, text: 'Completed' },
      failed: { color: 'bg-red-500', icon: XCircle, text: 'Failed' },
      abandoned: { color: 'bg-yellow-500', icon: AlertCircle, text: 'Abandoned' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.failed;
    const Icon = config.icon;

    return (
      <Badge className={`${config.color} text-white`}>
        <Icon className="w-3 h-3 mr-1" />
        {config.text}
      </Badge>
    );
  };

  const renderStars = (rating?: number) => {
    if (!rating) return <span className="text-gray-400">No rating</span>;

    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`w-4 h-4 ${
              star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
            }`}
          />
        ))}
        <span className="ml-1 text-sm text-gray-600">({rating})</span>
      </div>
    );
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchCalls(1);
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setCurrentPage(1);
  };

  if (loading && calls.length === 0) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading call history...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-6">
            <div className="flex items-center text-red-600">
              <AlertCircle className="w-5 h-5 mr-2" />
              <span>Error loading call history: {error}</span>
            </div>
            <Button
              onClick={() => fetchCalls(currentPage)}
              className="mt-4"
              variant="outline"
            >
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      <div className="container mx-auto p-6">
        {/* Navigation */}
        <nav className="flex items-center gap-6 mb-6 pb-4 border-b border-gray-200 dark:border-gray-700">
          <Link
            href="/"
            className="text-lg font-medium text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100 transition-colors"
          >
            Agents
          </Link>
          <Link
            href="/calls"
            className="text-lg font-semibold text-blue-600 hover:text-blue-700 transition-colors flex items-center gap-2"
          >
            <Phone className="w-4 h-4" />
            Call History
          </Link>
        </nav>

        <div className="mb-6">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2">
            Call History
          </h1>
          <p className="text-muted-foreground text-lg">Monitor and analyze all voice agent calls</p>
        </div>

      {/* Search and Filters */}
      <Card className="mb-6">
        <CardContent className="p-6">
          <form onSubmit={handleSearch} className="space-y-4">
            <div className="flex gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Search transcripts..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Button type="submit">
                <Search className="w-4 h-4 mr-2" />
                Search
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              <Input
                placeholder="Agent ID"
                value={filters.agent_id}
                onChange={(e) => handleFilterChange('agent_id', e.target.value)}
              />
              <Input
                placeholder="User ID"
                value={filters.user_id}
                onChange={(e) => handleFilterChange('user_id', e.target.value)}
              />
              <select
                className="px-3 py-2 border border-gray-300 rounded-md"
                value={filters.call_status}
                onChange={(e) => handleFilterChange('call_status', e.target.value)}
              >
                <option value="">All Statuses</option>
                <option value="active">Active</option>
                <option value="completed">Completed</option>
                <option value="failed">Failed</option>
                <option value="abandoned">Abandoned</option>
              </select>
              <Input
                type="date"
                placeholder="Start Date"
                value={filters.start_date}
                onChange={(e) => handleFilterChange('start_date', e.target.value)}
              />
              <Input
                type="date"
                placeholder="End Date"
                value={filters.end_date}
                onChange={(e) => handleFilterChange('end_date', e.target.value)}
              />
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <Phone className="w-8 h-8 text-blue-500 mr-3" />
              <div>
                <p className="text-sm text-gray-600">Total Calls</p>
                <p className="text-2xl font-bold">{totalCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <CheckCircle className="w-8 h-8 text-green-500 mr-3" />
              <div>
                <p className="text-sm text-gray-600">Completed</p>
                <p className="text-2xl font-bold">
                  {calls.filter(c => c.call_status === 'completed').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <Clock className="w-8 h-8 text-purple-500 mr-3" />
              <div>
                <p className="text-sm text-gray-600">Avg Duration</p>
                <p className="text-2xl font-bold">
                  {formatDuration(
                    Math.round(
                      calls.reduce((sum, call) => sum + (call.call_duration_seconds || 0), 0) /
                      calls.filter(c => c.call_duration_seconds).length
                    )
                  )}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <Star className="w-8 h-8 text-yellow-500 mr-3" />
              <div>
                <p className="text-sm text-gray-600">Avg Rating</p>
                <p className="text-2xl font-bold">
                  {(
                    calls.reduce((sum, call) => sum + (call.user_satisfaction_rating || 0), 0) /
                    calls.filter(c => c.user_satisfaction_rating).length
                  ).toFixed(1) || 'N/A'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Calls Table */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Recent Calls</CardTitle>
            <Button variant="outline" size="sm">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {calls.length === 0 ? (
            <div className="text-center py-8">
              <Phone className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">No calls found</p>
            </div>
          ) : (
            <div className="space-y-4">
              {calls.map((call) => (
                <div
                  key={call.id}
                  className="border rounded-lg p-4 hover:bg-gray-50 cursor-pointer transition-colors"
                  onClick={() => {
                    setSelectedCall(call);
                    setShowModal(true);
                  }}
                >
                  <div className="flex justify-between items-start mb-2">
                    <div className="flex items-center space-x-3">
                      {getStatusBadge(call.call_status)}
                      <span className="font-medium text-gray-900">
                        Call {call.call_id.slice(0, 8)}...
                      </span>
                      {call.call_recording_url ? (
                        <Badge variant="secondary" className="bg-green-100 text-green-700">
                          <Video className="w-3 h-3 mr-1" />
                          Recorded
                        </Badge>
                      ) : (
                        <Badge variant="secondary" className="bg-gray-100 text-gray-500">
                          <VideoOff className="w-3 h-3 mr-1" />
                          No Recording
                        </Badge>
                      )}
                    </div>
                    <div className="text-right text-sm text-gray-600">
                      <div className="flex items-center">
                        <Calendar className="w-4 h-4 mr-1" />
                        {formatDate(call.start_time)}
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Duration:</span>
                      <span className="ml-1 font-medium">
                        {formatDuration(call.call_duration_seconds)}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-600">User:</span>
                      <span className="ml-1 font-medium">
                        {call.user_id || 'Anonymous'}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-600">Cost:</span>
                      <span className="ml-1 font-medium">
                        ${call.estimated_cost.toFixed(4)}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-600">Rating:</span>
                      <span className="ml-1">
                        {renderStars(call.user_satisfaction_rating)}
                      </span>
                    </div>
                  </div>

                  {call.full_transcript && (
                    <div className="mt-3 p-3 bg-gray-100 rounded text-sm">
                      <p className="text-gray-700 line-clamp-2">
                        {call.full_transcript.slice(0, 200)}...
                      </p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-between items-center mt-6 pt-4 border-t">
              <div className="text-sm text-gray-600">
                Showing {((currentPage - 1) * 20) + 1} to {Math.min(currentPage * 20, totalCount)} of {totalCount} calls
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="w-4 h-4" />
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                >
                  Next
                  <ChevronRight className="w-4 h-4" />
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Call Details Modal */}
      <CallDetailsModal
        call={selectedCall}
        isOpen={showModal}
        onClose={() => {
          setShowModal(false);
          setSelectedCall(null);
        }}
      />
      </div>
    </div>
  );
}
