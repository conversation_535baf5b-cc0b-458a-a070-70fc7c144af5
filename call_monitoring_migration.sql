-- Call Monitoring Enhancement Migration
-- This migration creates comprehensive call tracking infrastructure

-- =====================================================
-- 1. Create Enhanced call_history Table
-- =====================================================

CREATE TABLE IF NOT EXISTS call_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    call_id TEXT UNIQUE NOT NULL,
    agent_id UUID REFERENCES agents(id) ON DELETE CASCADE,
    user_id TEXT,
    room_url TEXT NOT NULL,
    
    -- Call Timing
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE,
    call_duration_seconds INTEGER,
    
    -- Call Content
    full_transcript TEXT,
    call_recording_url TEXT,
    
    -- Call Quality & Metrics
    call_status TEXT DEFAULT 'active' CHECK (call_status IN ('active', 'completed', 'failed', 'abandoned')),
    call_quality_score DECIMAL(3,2), -- 1.00 to 5.00
    user_satisfaction_rating INTEGER CHECK (user_satisfaction_rating >= 1 AND user_satisfaction_rating <= 5),
    
    -- Service Configuration Used
    stt_service TEXT,
    llm_service TEXT,
    tts_service TEXT,
    stt_config JSONB DEFAULT '{}',
    llm_config JSONB DEFAULT '{}',
    tts_config JSONB DEFAULT '{}',
    
    -- Usage Metrics
    total_tokens_used INTEGER DEFAULT 0,
    stt_duration_seconds INTEGER DEFAULT 0,
    tts_characters_generated INTEGER DEFAULT 0,
    estimated_cost DECIMAL(10,4) DEFAULT 0.00,
    
    -- Technical Metrics
    average_response_time_ms INTEGER,
    error_count INTEGER DEFAULT 0,
    interruption_count INTEGER DEFAULT 0,
    
    -- Additional Data
    user_agent TEXT,
    ip_address INET,
    country_code TEXT,
    language_detected TEXT,
    conversation_summary TEXT,
    tags TEXT[] DEFAULT '{}',
    metadata JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 2. Create Call Participants Table
-- =====================================================

CREATE TABLE IF NOT EXISTS call_participants (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    call_id UUID REFERENCES call_history(id) ON DELETE CASCADE,
    participant_id TEXT NOT NULL,
    participant_type TEXT CHECK (participant_type IN ('user', 'agent', 'bot')) DEFAULT 'user',
    participant_name TEXT,
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    left_at TIMESTAMP WITH TIME ZONE,
    duration_seconds INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 3. Create Call Events Table
-- =====================================================

CREATE TABLE IF NOT EXISTS call_events (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    call_id UUID REFERENCES call_history(id) ON DELETE CASCADE,
    event_type TEXT NOT NULL,
    event_data JSONB DEFAULT '{}',
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 4. Create Indexes for Performance
-- =====================================================

-- Call History Indexes
CREATE INDEX IF NOT EXISTS idx_call_history_call_id ON call_history(call_id);
CREATE INDEX IF NOT EXISTS idx_call_history_agent_id ON call_history(agent_id);
CREATE INDEX IF NOT EXISTS idx_call_history_user_id ON call_history(user_id);
CREATE INDEX IF NOT EXISTS idx_call_history_start_time ON call_history(start_time);
CREATE INDEX IF NOT EXISTS idx_call_history_call_status ON call_history(call_status);
CREATE INDEX IF NOT EXISTS idx_call_history_created_at ON call_history(created_at);

-- Call Participants Indexes
CREATE INDEX IF NOT EXISTS idx_call_participants_call_id ON call_participants(call_id);
CREATE INDEX IF NOT EXISTS idx_call_participants_participant_id ON call_participants(participant_id);
CREATE INDEX IF NOT EXISTS idx_call_participants_type ON call_participants(participant_type);

-- Call Events Indexes
CREATE INDEX IF NOT EXISTS idx_call_events_call_id ON call_events(call_id);
CREATE INDEX IF NOT EXISTS idx_call_events_type ON call_events(event_type);
CREATE INDEX IF NOT EXISTS idx_call_events_timestamp ON call_events(timestamp);

-- =====================================================
-- 5. Create Triggers for Updated At
-- =====================================================

-- Update call_history updated_at trigger
CREATE TRIGGER update_call_history_updated_at 
    BEFORE UPDATE ON call_history
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 6. Create Views for Analytics
-- =====================================================

-- Call Analytics Summary View
CREATE OR REPLACE VIEW call_analytics_summary AS
SELECT
    DATE(start_time) as call_date,
    agent_id,
    COUNT(*) as total_calls,
    COUNT(CASE WHEN call_status = 'completed' THEN 1 END) as completed_calls,
    COUNT(CASE WHEN call_status = 'failed' THEN 1 END) as failed_calls,
    COUNT(CASE WHEN call_status = 'abandoned' THEN 1 END) as abandoned_calls,
    AVG(call_duration_seconds) as avg_duration_seconds,
    AVG(call_quality_score) as avg_quality_score,
    AVG(user_satisfaction_rating) as avg_satisfaction_rating,
    SUM(total_tokens_used) as total_tokens,
    SUM(estimated_cost) as total_cost,
    AVG(average_response_time_ms) as avg_response_time_ms
FROM call_history
WHERE start_time >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY DATE(start_time), agent_id;

-- Active Calls View
CREATE OR REPLACE VIEW active_calls AS
SELECT
    ch.*,
    a.name as agent_name,
    EXTRACT(EPOCH FROM (NOW() - ch.start_time))::INTEGER as current_duration_seconds
FROM call_history ch
LEFT JOIN agents a ON ch.agent_id = a.id
WHERE ch.call_status = 'active'
ORDER BY ch.start_time DESC;

-- =====================================================
-- 7. Data Migration from call_log (if exists)
-- =====================================================

-- Migrate existing call_log data to call_history
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'call_log') THEN
        INSERT INTO call_history (
            call_id,
            agent_id,
            room_url,
            start_time,
            end_time,
            call_duration_seconds,
            full_transcript,
            call_status,
            created_at
        )
        SELECT
            COALESCE(id::TEXT, gen_random_uuid()::TEXT) as call_id,
            agent_id::UUID,
            room_url,
            COALESCE(start_time::TIMESTAMP WITH TIME ZONE, created_at),
            end_time::TIMESTAMP WITH TIME ZONE,
            call_duration_seconds,
            full_transcript,
            CASE 
                WHEN status = 'running' THEN 'active'
                WHEN status = 'completed' THEN 'completed'
                ELSE 'failed'
            END as call_status,
            created_at
        FROM call_log
        WHERE NOT EXISTS (
            SELECT 1 FROM call_history 
            WHERE call_history.room_url = call_log.room_url 
            AND call_history.start_time = call_log.start_time
        );
        
        RAISE NOTICE 'Migrated data from call_log to call_history';
    END IF;
END $$;

-- =====================================================
-- 8. Create RLS Policies (if needed)
-- =====================================================

-- Enable RLS on call_history table
ALTER TABLE call_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE call_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE call_events ENABLE ROW LEVEL SECURITY;

-- Create policies for authenticated access
CREATE POLICY "Authenticated users can view call history" ON call_history
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can insert call history" ON call_history
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can update call history" ON call_history
    FOR UPDATE USING (auth.role() = 'authenticated');

-- Similar policies for related tables
CREATE POLICY "Authenticated users can manage call participants" ON call_participants
    FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can manage call events" ON call_events
    FOR ALL USING (auth.role() = 'authenticated');

-- =====================================================
-- 9. Comments for Documentation
-- =====================================================

COMMENT ON TABLE call_history IS 'Comprehensive call tracking and monitoring data';
COMMENT ON TABLE call_participants IS 'Tracks all participants in each call';
COMMENT ON TABLE call_events IS 'Detailed event log for each call';

COMMENT ON COLUMN call_history.call_id IS 'Unique identifier for the call session';
COMMENT ON COLUMN call_history.user_id IS 'Identifier for the calling user';
COMMENT ON COLUMN call_history.call_quality_score IS 'Technical quality score (1.00-5.00)';
COMMENT ON COLUMN call_history.user_satisfaction_rating IS 'User satisfaction rating (1-5 stars)';
COMMENT ON COLUMN call_history.estimated_cost IS 'Estimated cost in USD for the call';

-- =====================================================
-- Migration Complete
-- =====================================================

-- Add migration tracking
INSERT INTO public.schema_migrations (version, applied_at) 
VALUES ('call_monitoring_v1', NOW())
ON CONFLICT (version) DO NOTHING;
