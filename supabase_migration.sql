-- Supabase Migration for Enhanced Voice AI Agent Configuration
-- This file contains the SQL commands to update the database schema
-- to support the new service configuration capabilities

-- =====================================================
-- 1. Update existing agents table to support new fields
-- =====================================================

-- Add new service type columns
ALTER TABLE agents
ADD COLUMN IF NOT EXISTS s2s_service TEXT,
ADD COLUMN IF NOT EXISTS conversation_config JSONB DEFAULT '{}';

-- Update existing configuration columns to be more flexible
ALTER TABLE agents
ALTER COLUMN stt_config TYPE JSONB USING stt_config::JSONB,
ALTER COLUMN llm_config TYPE JSONB USING llm_config::J<PERSON><PERSON><PERSON>,
ALTER COLUMN tts_config TYPE JSONB USING tts_config::JSONB,
ALTER COLUMN vad_config TYPE JSONB USING vad_config::JSONB;

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_agents_stt_service ON agents(stt_service);
CREATE INDEX IF NOT EXISTS idx_agents_llm_service ON agents(llm_service);
CREATE INDEX IF NOT EXISTS idx_agents_tts_service ON agents(tts_service);
CREATE INDEX IF NOT EXISTS idx_agents_s2s_service ON agents(s2s_service);
CREATE INDEX IF NOT EXISTS idx_agents_status ON agents(status);
CREATE INDEX IF NOT EXISTS idx_agents_category ON agents(category);

-- =====================================================
-- 2. Create service capabilities table
-- =====================================================

CREATE TABLE IF NOT EXISTS service_capabilities (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    service_type TEXT NOT NULL CHECK (service_type IN ('stt', 'llm', 'tts', 's2s', 'vad')),
    service_name TEXT NOT NULL,
    display_name TEXT NOT NULL,
    description TEXT NOT NULL,
    supported_languages TEXT[] DEFAULT '{}',
    supported_models TEXT[] DEFAULT '{}',
    features TEXT[] DEFAULT '{}',
    config_schema JSONB NOT NULL DEFAULT '{}',
    pricing_tier TEXT CHECK (pricing_tier IN ('free', 'paid', 'enterprise')),
    latency_rating TEXT CHECK (latency_rating IN ('ultra_low', 'low', 'medium', 'high')),
    quality_rating TEXT CHECK (quality_rating IN ('basic', 'good', 'excellent')),
    requires_api_key BOOLEAN DEFAULT true,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(service_type, service_name)
);

-- Create indexes for service capabilities
CREATE INDEX IF NOT EXISTS idx_service_capabilities_type ON service_capabilities(service_type);
CREATE INDEX IF NOT EXISTS idx_service_capabilities_name ON service_capabilities(service_name);
CREATE INDEX IF NOT EXISTS idx_service_capabilities_active ON service_capabilities(is_active);

-- =====================================================
-- 3. Create service configuration templates table
-- =====================================================

CREATE TABLE IF NOT EXISTS service_configuration_templates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    template_id TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    service_type TEXT NOT NULL,
    service_name TEXT NOT NULL,
    use_case TEXT NOT NULL,
    configuration JSONB NOT NULL DEFAULT '{}',
    tags TEXT[] DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    FOREIGN KEY (service_type, service_name) REFERENCES service_capabilities(service_type, service_name)
);

-- Create indexes for templates
CREATE INDEX IF NOT EXISTS idx_templates_service_type ON service_configuration_templates(service_type);
CREATE INDEX IF NOT EXISTS idx_templates_service_name ON service_configuration_templates(service_name);
CREATE INDEX IF NOT EXISTS idx_templates_use_case ON service_configuration_templates(use_case);
CREATE INDEX IF NOT EXISTS idx_templates_active ON service_configuration_templates(is_active);

-- =====================================================
-- 4. Create agent configuration presets table
-- =====================================================

CREATE TABLE IF NOT EXISTS agent_configuration_presets (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    preset_id TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    category TEXT NOT NULL,
    use_case TEXT NOT NULL,
    stt_service TEXT NOT NULL,
    llm_service TEXT NOT NULL,
    tts_service TEXT NOT NULL,
    stt_config JSONB NOT NULL DEFAULT '{}',
    llm_config JSONB NOT NULL DEFAULT '{}',
    tts_config JSONB NOT NULL DEFAULT '{}',
    vad_config JSONB DEFAULT '{}',
    conversation_config JSONB DEFAULT '{}',
    performance_profile TEXT CHECK (performance_profile IN ('speed', 'balanced', 'quality')),
    cost_profile TEXT CHECK (cost_profile IN ('budget', 'standard', 'premium')),
    complexity_level TEXT CHECK (complexity_level IN ('basic', 'intermediate', 'advanced')),
    tags TEXT[] DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for presets
CREATE INDEX IF NOT EXISTS idx_presets_category ON agent_configuration_presets(category);
CREATE INDEX IF NOT EXISTS idx_presets_use_case ON agent_configuration_presets(use_case);
CREATE INDEX IF NOT EXISTS idx_presets_performance ON agent_configuration_presets(performance_profile);
CREATE INDEX IF NOT EXISTS idx_presets_cost ON agent_configuration_presets(cost_profile);
CREATE INDEX IF NOT EXISTS idx_presets_active ON agent_configuration_presets(is_active);

-- =====================================================
-- 5. Create agent analytics table for enhanced tracking
-- =====================================================

CREATE TABLE IF NOT EXISTS agent_analytics_enhanced (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    agent_id UUID NOT NULL REFERENCES agents(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    stt_service TEXT,
    llm_service TEXT,
    tts_service TEXT,
    total_calls INTEGER DEFAULT 0,
    total_duration INTEGER DEFAULT 0, -- in seconds
    total_tokens INTEGER DEFAULT 0,
    total_cost DECIMAL(10,4) DEFAULT 0.00,
    average_response_time INTEGER DEFAULT 0, -- in milliseconds
    error_count INTEGER DEFAULT 0,
    success_rate DECIMAL(5,2) DEFAULT 0.00,
    user_satisfaction_score DECIMAL(3,2), -- 1.00 to 5.00
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(agent_id, date)
);

-- Create indexes for analytics
CREATE INDEX IF NOT EXISTS idx_analytics_agent_date ON agent_analytics_enhanced(agent_id, date);
CREATE INDEX IF NOT EXISTS idx_analytics_date ON agent_analytics_enhanced(date);
CREATE INDEX IF NOT EXISTS idx_analytics_services ON agent_analytics_enhanced(stt_service, llm_service, tts_service);

-- =====================================================
-- 6. Create service usage tracking table
-- =====================================================

CREATE TABLE IF NOT EXISTS service_usage_tracking (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    agent_id UUID NOT NULL REFERENCES agents(id) ON DELETE CASCADE,
    service_type TEXT NOT NULL,
    service_name TEXT NOT NULL,
    usage_date DATE NOT NULL,
    request_count INTEGER DEFAULT 0,
    total_duration INTEGER DEFAULT 0, -- in seconds
    total_tokens INTEGER DEFAULT 0,
    estimated_cost DECIMAL(10,4) DEFAULT 0.00,
    error_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(agent_id, service_type, service_name, usage_date)
);

-- Create indexes for usage tracking
CREATE INDEX IF NOT EXISTS idx_usage_agent_service ON service_usage_tracking(agent_id, service_type, service_name);
CREATE INDEX IF NOT EXISTS idx_usage_date ON service_usage_tracking(usage_date);

-- =====================================================
-- 7. Create triggers for updated_at timestamps
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for all tables with updated_at
CREATE TRIGGER update_agents_updated_at BEFORE UPDATE ON agents
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_service_capabilities_updated_at BEFORE UPDATE ON service_capabilities
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_service_templates_updated_at BEFORE UPDATE ON service_configuration_templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_agent_presets_updated_at BEFORE UPDATE ON agent_configuration_presets
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_analytics_updated_at BEFORE UPDATE ON agent_analytics_enhanced
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_usage_tracking_updated_at BEFORE UPDATE ON service_usage_tracking
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 8. Insert initial service capabilities data
-- =====================================================

-- Insert STT service capabilities
INSERT INTO service_capabilities (service_type, service_name, display_name, description, supported_languages, supported_models, features, config_schema, pricing_tier, latency_rating, quality_rating, requires_api_key) VALUES
('stt', 'deepgram', 'Deepgram', 'High-accuracy real-time speech-to-text with advanced features',
 ARRAY['en', 'en-US', 'en-GB', 'es', 'fr', 'de', 'it', 'pt', 'ja', 'ko', 'zh'],
 ARRAY['nova-2', 'nova-2-general', 'nova-2-meeting', 'nova-2-conversationalai'],
 ARRAY['real_time', 'interim_results', 'smart_format', 'punctuation', 'vad_events'],
 '{"type": "object", "properties": {"model": {"type": "string", "default": "nova-2"}, "language": {"type": "string", "default": "en-US"}, "smart_format": {"type": "boolean", "default": true}}}',
 'paid', 'low', 'excellent', true),

('stt', 'openai_whisper', 'OpenAI Whisper', 'OpenAI''s robust speech recognition model',
 ARRAY['en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh'],
 ARRAY['whisper-1'],
 ARRAY['multilingual', 'translation', 'timestamps'],
 '{"type": "object", "properties": {"model": {"type": "string", "default": "whisper-1"}, "language": {"type": "string", "default": "en"}, "temperature": {"type": "number", "default": 0}}}',
 'paid', 'medium', 'excellent', true),

('stt', 'assemblyai', 'AssemblyAI', 'AI-powered speech recognition with advanced analytics',
 ARRAY['en', 'es', 'fr', 'de', 'it', 'pt', 'nl', 'hi', 'ja'],
 ARRAY['best', 'nano'],
 ARRAY['speaker_diarization', 'sentiment_analysis', 'entity_detection'],
 '{"type": "object", "properties": {"model": {"type": "string", "default": "best"}, "speaker_labels": {"type": "boolean", "default": false}}}',
 'paid', 'medium', 'excellent', true);

-- Insert LLM service capabilities
INSERT INTO service_capabilities (service_type, service_name, display_name, description, supported_languages, supported_models, features, config_schema, pricing_tier, latency_rating, quality_rating, requires_api_key) VALUES
('llm', 'openai', 'OpenAI GPT', 'Advanced language models from OpenAI',
 ARRAY['en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh'],
 ARRAY['gpt-4o', 'gpt-4o-mini', 'gpt-4-turbo'],
 ARRAY['function_calling', 'vision', 'streaming', 'json_mode'],
 '{"type": "object", "properties": {"model": {"type": "string", "default": "gpt-4o"}, "temperature": {"type": "number", "default": 0.7}, "max_tokens": {"type": "integer", "default": 1000}}}',
 'paid', 'low', 'excellent', true),

('llm', 'anthropic', 'Anthropic Claude', 'Constitutional AI models from Anthropic',
 ARRAY['en', 'es', 'fr', 'de', 'it', 'pt', 'ja', 'ko', 'zh'],
 ARRAY['claude-3-5-sonnet-20241022', 'claude-3-5-haiku-20241022'],
 ARRAY['long_context', 'function_calling', 'vision', 'safety_focused'],
 '{"type": "object", "properties": {"model": {"type": "string", "default": "claude-3-5-sonnet-20241022"}, "temperature": {"type": "number", "default": 0.7}, "max_tokens": {"type": "integer", "default": 1000}}}',
 'paid', 'low', 'excellent', true);

-- Insert TTS service capabilities
INSERT INTO service_capabilities (service_type, service_name, display_name, description, supported_languages, supported_models, features, config_schema, pricing_tier, latency_rating, quality_rating, requires_api_key) VALUES
('tts', 'elevenlabs', 'ElevenLabs', 'High-quality AI voice synthesis with emotion control',
 ARRAY['en', 'es', 'fr', 'de', 'it', 'pt', 'pl', 'hi', 'ar', 'zh'],
 ARRAY['eleven_flash_v2_5', 'eleven_turbo_v2_5'],
 ARRAY['voice_cloning', 'emotion_control', 'style_control', 'streaming'],
 '{"type": "object", "properties": {"voice_id": {"type": "string", "default": "21m00Tcm4TlvDq8ikWAM"}, "model": {"type": "string", "default": "eleven_flash_v2_5"}, "stability": {"type": "number", "default": 0.5}}}',
 'paid', 'low', 'excellent', true),

('tts', 'openai', 'OpenAI TTS', 'OpenAI''s text-to-speech with natural voices',
 ARRAY['en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh'],
 ARRAY['tts-1', 'tts-1-hd'],
 ARRAY['multiple_voices', 'streaming', 'high_quality'],
 '{"type": "object", "properties": {"model": {"type": "string", "default": "tts-1"}, "voice": {"type": "string", "default": "alloy"}, "speed": {"type": "number", "default": 1.0}}}',
 'paid', 'low', 'good', true);

-- =====================================================
-- 9. Insert initial configuration templates
-- =====================================================

INSERT INTO service_configuration_templates (template_id, name, description, service_type, service_name, use_case, configuration, tags) VALUES
('stt_high_accuracy', 'High Accuracy Transcription', 'Optimized for maximum transcription accuracy', 'stt', 'deepgram', 'business_meetings',
 '{"model": "nova-2-meeting", "language": "en-US", "smart_format": true, "punctuate": true, "interim_results": true}',
 ARRAY['accuracy', 'business', 'meetings']),

('stt_low_latency', 'Low Latency Transcription', 'Optimized for real-time conversation', 'stt', 'deepgram', 'real_time_chat',
 '{"model": "nova-2-conversationalai", "language": "en-US", "smart_format": false, "punctuate": false, "interim_results": true}',
 ARRAY['speed', 'real_time', 'conversation']),

('llm_creative', 'Creative Assistant', 'High creativity for content generation', 'llm', 'openai', 'content_creation',
 '{"model": "gpt-4o", "temperature": 0.9, "max_tokens": 2000, "top_p": 0.9}',
 ARRAY['creative', 'content', 'writing']),

('llm_analytical', 'Analytical Assistant', 'Focused on accuracy and analysis', 'llm', 'openai', 'data_analysis',
 '{"model": "gpt-4o", "temperature": 0.1, "max_tokens": 1500, "top_p": 0.95}',
 ARRAY['analytical', 'accuracy', 'data']),

('tts_natural', 'Natural Conversation', 'Natural-sounding voice for conversations', 'tts', 'elevenlabs', 'conversation',
 '{"voice_id": "21m00Tcm4TlvDq8ikWAM", "model": "eleven_flash_v2_5", "stability": 0.7, "similarity_boost": 0.8}',
 ARRAY['natural', 'conversation', 'friendly']),

('tts_professional', 'Professional Voice', 'Clear, professional voice for business', 'tts', 'elevenlabs', 'business',
 '{"voice_id": "EXAVITQu4vr4xnSDxMaL", "model": "eleven_flash_v2_5", "stability": 0.8, "similarity_boost": 0.9}',
 ARRAY['professional', 'business', 'clear']);

-- =====================================================
-- 10. Create RLS (Row Level Security) policies if needed
-- =====================================================

-- Enable RLS on sensitive tables
ALTER TABLE service_capabilities ENABLE ROW LEVEL SECURITY;
ALTER TABLE service_configuration_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE agent_configuration_presets ENABLE ROW LEVEL SECURITY;

-- Create policies for public read access to service capabilities and templates
CREATE POLICY "Public read access for service capabilities" ON service_capabilities
    FOR SELECT USING (is_active = true);

CREATE POLICY "Public read access for service templates" ON service_configuration_templates
    FOR SELECT USING (is_active = true);

CREATE POLICY "Public read access for agent presets" ON agent_configuration_presets
    FOR SELECT USING (is_active = true);

-- =====================================================
-- 11. Create views for easier data access
-- =====================================================

-- View for active services with their templates
CREATE OR REPLACE VIEW active_services_with_templates AS
SELECT
    sc.*,
    COALESCE(
        json_agg(
            json_build_object(
                'template_id', sct.template_id,
                'name', sct.name,
                'description', sct.description,
                'use_case', sct.use_case,
                'configuration', sct.configuration,
                'tags', sct.tags
            )
        ) FILTER (WHERE sct.id IS NOT NULL),
        '[]'::json
    ) as templates
FROM service_capabilities sc
LEFT JOIN service_configuration_templates sct ON sc.service_type = sct.service_type
    AND sc.service_name = sct.service_name
    AND sct.is_active = true
WHERE sc.is_active = true
GROUP BY sc.id, sc.service_type, sc.service_name, sc.display_name, sc.description,
         sc.supported_languages, sc.supported_models, sc.features, sc.config_schema,
         sc.pricing_tier, sc.latency_rating, sc.quality_rating, sc.requires_api_key,
         sc.is_active, sc.created_at, sc.updated_at;

-- View for agent analytics summary
CREATE OR REPLACE VIEW agent_analytics_summary AS
SELECT
    a.id as agent_id,
    a.name as agent_name,
    a.stt_service,
    a.llm_service,
    a.tts_service,
    COUNT(aae.id) as analytics_days,
    SUM(aae.total_calls) as total_calls,
    SUM(aae.total_duration) as total_duration,
    AVG(aae.average_response_time) as avg_response_time,
    AVG(aae.success_rate) as avg_success_rate,
    SUM(aae.total_cost) as total_cost
FROM agents a
LEFT JOIN agent_analytics_enhanced aae ON a.id = aae.agent_id
WHERE a.status = 'active'
GROUP BY a.id, a.name, a.stt_service, a.llm_service, a.tts_service;

-- =====================================================
-- Migration Complete
-- =====================================================

-- Add a comment to track migration
COMMENT ON TABLE service_capabilities IS 'Enhanced service capabilities for voice AI agents - Migration v1.0';
COMMENT ON TABLE service_configuration_templates IS 'Configuration templates for quick service setup - Migration v1.0';
COMMENT ON TABLE agent_configuration_presets IS 'Complete agent configuration presets - Migration v1.0';
COMMENT ON TABLE agent_analytics_enhanced IS 'Enhanced analytics tracking for agents - Migration v1.0';
COMMENT ON TABLE service_usage_tracking IS 'Service usage tracking for cost and performance monitoring - Migration v1.0';

-- =====================================================
-- MIGRATION INSTRUCTIONS
-- =====================================================

/*
HOW TO APPLY THIS MIGRATION TO SUPABASE:

1. **Backup Your Database** (IMPORTANT!)
   - Go to Supabase Dashboard > Settings > Database
   - Create a backup before applying this migration

2. **Apply the Migration**
   Option A - Using Supabase Dashboard:
   - Go to Supabase Dashboard > SQL Editor
   - Copy and paste this entire file
   - Click "Run" to execute

   Option B - Using Supabase CLI:
   - Save this file as `20241202_enhanced_agent_config.sql`
   - Run: `supabase db push`

3. **Verify the Migration**
   Run these queries to verify tables were created:
   ```sql
   SELECT table_name FROM information_schema.tables
   WHERE table_schema = 'public'
   AND table_name IN ('service_capabilities', 'service_configuration_templates', 'agent_configuration_presets');
   ```

4. **Update Environment Variables**
   Add these new API keys to your environment if you plan to use the services:
   - ASSEMBLYAI_API_KEY (for AssemblyAI STT)
   - TOGETHER_API_KEY (for Together AI LLM)
   - FIREWORKS_API_KEY (for Fireworks AI LLM)
   - PLAYHT_API_KEY (for PlayHT TTS)
   - LMNT_API_KEY (for LMNT TTS)

5. **Test the Migration**
   After applying, test with:
   ```sql
   SELECT COUNT(*) FROM service_capabilities;
   SELECT COUNT(*) FROM service_configuration_templates;
   ```
   You should see service capabilities and templates data.

6. **Update Your Application**
   - Restart your application server
   - Test the new service configuration endpoints
   - Verify agent creation with enhanced configurations

ROLLBACK INSTRUCTIONS (if needed):
If you need to rollback this migration:
```sql
-- Drop new tables (in reverse order due to foreign keys)
DROP TABLE IF EXISTS service_usage_tracking;
DROP TABLE IF EXISTS agent_analytics_enhanced;
DROP TABLE IF EXISTS agent_configuration_presets;
DROP TABLE IF EXISTS service_configuration_templates;
DROP TABLE IF EXISTS service_capabilities;

-- Remove new columns from agents table
ALTER TABLE agents
DROP COLUMN IF EXISTS s2s_service,
DROP COLUMN IF EXISTS conversation_config;

-- Drop the update function and triggers
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;
```

NOTES:
- This migration is designed to be non-destructive to existing data
- Existing agents will continue to work with their current configurations
- New configuration options will be available for new agents
- The migration includes sample data for immediate testing
*/

-- =====================================================
-- 12. CALL MONITORING ENHANCEMENT MIGRATION
-- =====================================================

-- Create Enhanced call_history Table
CREATE TABLE IF NOT EXISTS call_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    call_id TEXT UNIQUE NOT NULL,
    agent_id UUID REFERENCES agents(id) ON DELETE CASCADE,
    user_id TEXT,
    room_url TEXT NOT NULL,

    -- Call Timing
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE,
    call_duration_seconds INTEGER,

    -- Call Content
    full_transcript TEXT,
    call_recording_url TEXT,

    -- Call Quality & Metrics
    call_status TEXT DEFAULT 'active' CHECK (call_status IN ('active', 'completed', 'failed', 'abandoned')),
    call_quality_score DECIMAL(3,2), -- 1.00 to 5.00
    user_satisfaction_rating INTEGER CHECK (user_satisfaction_rating >= 1 AND user_satisfaction_rating <= 5),

    -- Service Configuration Used
    stt_service TEXT,
    llm_service TEXT,
    tts_service TEXT,
    stt_config JSONB DEFAULT '{}',
    llm_config JSONB DEFAULT '{}',
    tts_config JSONB DEFAULT '{}',

    -- Usage Metrics
    total_tokens_used INTEGER DEFAULT 0,
    stt_duration_seconds INTEGER DEFAULT 0,
    tts_characters_generated INTEGER DEFAULT 0,
    estimated_cost DECIMAL(10,4) DEFAULT 0.00,

    -- Technical Metrics
    average_response_time_ms INTEGER,
    error_count INTEGER DEFAULT 0,
    interruption_count INTEGER DEFAULT 0,

    -- Additional Data
    user_agent TEXT,
    ip_address INET,
    country_code TEXT,
    language_detected TEXT,
    conversation_summary TEXT,
    tags TEXT[] DEFAULT '{}',
    metadata JSONB DEFAULT '{}',

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create Call Participants Table
CREATE TABLE IF NOT EXISTS call_participants (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    call_id UUID REFERENCES call_history(id) ON DELETE CASCADE,
    participant_id TEXT NOT NULL,
    participant_type TEXT CHECK (participant_type IN ('user', 'agent', 'bot')) DEFAULT 'user',
    participant_name TEXT,
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    left_at TIMESTAMP WITH TIME ZONE,
    duration_seconds INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create Call Events Table
CREATE TABLE IF NOT EXISTS call_events (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    call_id UUID REFERENCES call_history(id) ON DELETE CASCADE,
    event_type TEXT NOT NULL,
    event_data JSONB DEFAULT '{}',
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create Indexes for Performance
-- Call History Indexes
CREATE INDEX IF NOT EXISTS idx_call_history_call_id ON call_history(call_id);
CREATE INDEX IF NOT EXISTS idx_call_history_agent_id ON call_history(agent_id);
CREATE INDEX IF NOT EXISTS idx_call_history_user_id ON call_history(user_id);
CREATE INDEX IF NOT EXISTS idx_call_history_start_time ON call_history(start_time);
CREATE INDEX IF NOT EXISTS idx_call_history_call_status ON call_history(call_status);
CREATE INDEX IF NOT EXISTS idx_call_history_created_at ON call_history(created_at);

-- Call Participants Indexes
CREATE INDEX IF NOT EXISTS idx_call_participants_call_id ON call_participants(call_id);
CREATE INDEX IF NOT EXISTS idx_call_participants_participant_id ON call_participants(participant_id);
CREATE INDEX IF NOT EXISTS idx_call_participants_type ON call_participants(participant_type);

-- Call Events Indexes
CREATE INDEX IF NOT EXISTS idx_call_events_call_id ON call_events(call_id);
CREATE INDEX IF NOT EXISTS idx_call_events_type ON call_events(event_type);
CREATE INDEX IF NOT EXISTS idx_call_events_timestamp ON call_events(timestamp);

-- Create Triggers for Updated At
CREATE TRIGGER update_call_history_updated_at
    BEFORE UPDATE ON call_history
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create Views for Analytics
-- Call Analytics Summary View
CREATE OR REPLACE VIEW call_analytics_summary AS
SELECT
    DATE(start_time) as call_date,
    agent_id,
    COUNT(*) as total_calls,
    COUNT(CASE WHEN call_status = 'completed' THEN 1 END) as completed_calls,
    COUNT(CASE WHEN call_status = 'failed' THEN 1 END) as failed_calls,
    COUNT(CASE WHEN call_status = 'abandoned' THEN 1 END) as abandoned_calls,
    AVG(call_duration_seconds) as avg_duration_seconds,
    AVG(call_quality_score) as avg_quality_score,
    AVG(user_satisfaction_rating) as avg_satisfaction_rating,
    SUM(total_tokens_used) as total_tokens,
    SUM(estimated_cost) as total_cost,
    AVG(average_response_time_ms) as avg_response_time_ms
FROM call_history
WHERE start_time >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY DATE(start_time), agent_id;

-- Active Calls View
CREATE OR REPLACE VIEW active_calls AS
SELECT
    ch.*,
    a.name as agent_name,
    EXTRACT(EPOCH FROM (NOW() - ch.start_time))::INTEGER as current_duration_seconds
FROM call_history ch
LEFT JOIN agents a ON ch.agent_id = a.id
WHERE ch.call_status = 'active'
ORDER BY ch.start_time DESC;

-- Data Migration from call_log (if exists)
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'call_log') THEN
        INSERT INTO call_history (
            call_id,
            agent_id,
            room_url,
            start_time,
            end_time,
            call_duration_seconds,
            full_transcript,
            call_status,
            created_at
        )
        SELECT
            COALESCE(id::TEXT, gen_random_uuid()::TEXT) as call_id,
            agent_id::UUID,
            room_url,
            COALESCE(start_time::TIMESTAMP WITH TIME ZONE, created_at),
            end_time::TIMESTAMP WITH TIME ZONE,
            call_duration_seconds,
            full_transcript,
            CASE
                WHEN status = 'running' THEN 'active'
                WHEN status = 'completed' THEN 'completed'
                ELSE 'failed'
            END as call_status,
            created_at
        FROM call_log
        WHERE NOT EXISTS (
            SELECT 1 FROM call_history
            WHERE call_history.room_url = call_log.room_url
            AND call_history.start_time = call_log.start_time
        );

        RAISE NOTICE 'Migrated data from call_log to call_history';
    END IF;
END $$;

-- Create RLS Policies
ALTER TABLE call_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE call_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE call_events ENABLE ROW LEVEL SECURITY;

-- Create policies for authenticated access
CREATE POLICY "Authenticated users can view call history" ON call_history
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can insert call history" ON call_history
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can update call history" ON call_history
    FOR UPDATE USING (auth.role() = 'authenticated');

-- Similar policies for related tables
CREATE POLICY "Authenticated users can manage call participants" ON call_participants
    FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can manage call events" ON call_events
    FOR ALL USING (auth.role() = 'authenticated');

-- Comments for Documentation
COMMENT ON TABLE call_history IS 'Comprehensive call tracking and monitoring data';
COMMENT ON TABLE call_participants IS 'Tracks all participants in each call';
COMMENT ON TABLE call_events IS 'Detailed event log for each call';

COMMENT ON COLUMN call_history.call_id IS 'Unique identifier for the call session';
COMMENT ON COLUMN call_history.user_id IS 'Identifier for the calling user';
COMMENT ON COLUMN call_history.call_quality_score IS 'Technical quality score (1.00-5.00)';
COMMENT ON COLUMN call_history.user_satisfaction_rating IS 'User satisfaction rating (1-5 stars)';
COMMENT ON COLUMN call_history.estimated_cost IS 'Estimated cost in USD for the call';

-- =====================================================
-- CALL MONITORING MIGRATION COMPLETE
-- =====================================================
