#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to add sample call history data for testing the call monitoring functionality.
"""

import asyncio
import uuid
from datetime import datetime, timezone, timedelta
import random
from supabase import create_client
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Supabase configuration
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_KEY")

if not SUPABASE_URL or not SUPABASE_KEY:
    print("Error: SUPABASE_URL and SUPABASE_KEY must be set in environment variables")
    exit(1)

# Initialize Supabase client
supabase = create_client(SUPABASE_URL, SUPABASE_KEY)

# Sample data
SAMPLE_TRANSCRIPTS = [
    "User: Hello, I need help with my account. Agent: I'd be happy to help you with your account. What specific issue are you experiencing? User: I can't log in to my dashboard. Agent: Let me help you troubleshoot that login issue. Can you tell me what error message you're seeing?",
    "User: Hi there, I'm interested in your premium plan. Agent: Great! I'd love to tell you about our premium features. What specific capabilities are you most interested in? User: I need better voice quality and longer call durations. Agent: Our premium plan offers crystal clear HD voice and unlimited call duration.",
    "User: Can you help me integrate this with my CRM? Agent: Absolutely! We have several CRM integrations available. Which CRM system are you using? User: We use Salesforce. Agent: Perfect! Our Salesforce integration is very robust and includes real-time sync.",
    "User: I'm having trouble with the voice recognition. Agent: I understand your concern about voice recognition accuracy. Let me help you optimize the settings. What language are you primarily using? User: English, but sometimes with an accent. Agent: We can adjust the accent recognition settings for better accuracy.",
    "User: What's your pricing for enterprise customers? Agent: For enterprise customers, we offer custom pricing based on your specific needs. How many users would you need to support? User: Around 500 users. Agent: For 500 users, we can create a tailored package with volume discounts."
]

SAMPLE_USERS = [
    "user_123", "user_456", "user_789", "user_abc", "user_def",
    "customer_001", "customer_002", "customer_003", "demo_user", "trial_user"
]

CALL_STATUSES = ["completed", "failed", "abandoned"]
STT_SERVICES = ["deepgram", "openai_whisper", "google_speech"]
LLM_SERVICES = ["openai_gpt4", "anthropic_claude", "openai_gpt35"]
TTS_SERVICES = ["elevenlabs", "openai_tts", "azure_speech"]
LANGUAGES = ["en-US", "en-GB", "es-ES", "fr-FR", "de-DE"]
COUNTRIES = ["US", "GB", "ES", "FR", "DE", "CA", "AU"]

def generate_sample_call():
    """Generate a sample call history entry"""
    start_time = datetime.now(timezone.utc) - timedelta(
        days=random.randint(0, 30),
        hours=random.randint(0, 23),
        minutes=random.randint(0, 59)
    )

    duration = random.randint(30, 1800)  # 30 seconds to 30 minutes
    end_time = start_time + timedelta(seconds=duration)

    status = random.choice(CALL_STATUSES)
    if status == "failed":
        end_time = start_time + timedelta(seconds=random.randint(5, 60))
        duration = int((end_time - start_time).total_seconds())
    elif status == "abandoned":
        end_time = start_time + timedelta(seconds=random.randint(10, 120))
        duration = int((end_time - start_time).total_seconds())

    tokens_used = random.randint(100, 5000)
    cost = tokens_used * 0.0001 + random.uniform(0.01, 0.50)  # Rough cost calculation

    return {
        "call_id": str(uuid.uuid4()),
        "user_id": random.choice(SAMPLE_USERS),
        "room_url": f"https://voxdiscover.daily.co/room_{uuid.uuid4().hex[:8]}",
        "start_time": start_time.isoformat(),
        "end_time": end_time.isoformat() if status != "active" else None,
        "call_duration_seconds": duration if status != "active" else None,
        "full_transcript": random.choice(SAMPLE_TRANSCRIPTS) if status == "completed" else None,
        "call_status": status,
        "call_quality_score": round(random.uniform(3.0, 5.0), 2) if status == "completed" else None,
        "user_satisfaction_rating": random.randint(3, 5) if status == "completed" and random.random() > 0.3 else None,
        "stt_service": random.choice(STT_SERVICES),
        "llm_service": random.choice(LLM_SERVICES),
        "tts_service": random.choice(TTS_SERVICES),
        "stt_config": {"language": random.choice(LANGUAGES), "model": "nova-2"},
        "llm_config": {"temperature": round(random.uniform(0.1, 1.0), 2), "max_tokens": random.randint(500, 2000)},
        "tts_config": {"voice": "alloy", "speed": round(random.uniform(0.8, 1.2), 2)},
        "total_tokens_used": tokens_used,
        "stt_duration_seconds": duration if status == "completed" else 0,
        "tts_characters_generated": random.randint(200, 2000) if status == "completed" else 0,
        "estimated_cost": round(cost, 4),
        "average_response_time_ms": random.randint(200, 2000) if status == "completed" else None,
        "error_count": random.randint(0, 3) if status == "failed" else 0,
        "interruption_count": random.randint(0, 5) if status == "completed" else 0,
        "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
        "country_code": random.choice(COUNTRIES),
        "language_detected": random.choice(LANGUAGES),
        "conversation_summary": "Customer inquiry about product features and pricing" if status == "completed" else None,
        "tags": random.sample(["support", "sales", "demo", "technical", "billing"], random.randint(1, 3)),
        "metadata": {
            "browser": "Chrome",
            "device": "desktop",
            "referrer": "https://voxdiscover.com"
        }
    }

async def get_agent_ids():
    """Get existing agent IDs from the database"""
    try:
        response = supabase.table("agents").select("id").execute()
        if response.data:
            return [agent["id"] for agent in response.data]
        else:
            print("No agents found in database. Creating sample calls without agent_id.")
            return []
    except Exception as e:
        print(f"Error fetching agents: {e}")
        return []

async def create_sample_calls(num_calls=50):
    """Create sample call history entries"""
    print(f"Creating {num_calls} sample call history entries...")

    # Get existing agent IDs
    agent_ids = await get_agent_ids()

    calls_created = 0
    for i in range(num_calls):
        try:
            call_data = generate_sample_call()

            # Assign random agent ID if available
            if agent_ids:
                call_data["agent_id"] = random.choice(agent_ids)

            # Insert into database
            response = supabase.table("call_history").insert(call_data).execute()

            if response.data:
                calls_created += 1
                if calls_created % 10 == 0:
                    print(f"Created {calls_created} calls...")
            else:
                print(f"Failed to create call {i+1}: {response}")

        except Exception as e:
            print(f"Error creating call {i+1}: {e}")

    print(f"Successfully created {calls_created} sample calls!")

async def create_sample_participants_and_events():
    """Create sample participants and events for existing calls"""
    print("Creating sample participants and events...")

    try:
        # Get some recent calls
        response = supabase.table("call_history").select("id, call_id").limit(20).execute()

        if not response.data:
            print("No calls found to add participants and events")
            return

        participants_created = 0
        events_created = 0

        for call in response.data:
            call_id = call["id"]

            # Add participants
            participants = [
                {
                    "call_id": call_id,
                    "participant_id": f"user_{uuid.uuid4().hex[:8]}",
                    "participant_type": "user",
                    "participant_name": "Customer",
                    "joined_at": datetime.now(timezone.utc).isoformat(),
                    "left_at": (datetime.now(timezone.utc) + timedelta(minutes=random.randint(1, 30))).isoformat(),
                    "duration_seconds": random.randint(60, 1800)
                },
                {
                    "call_id": call_id,
                    "participant_id": f"bot_{uuid.uuid4().hex[:8]}",
                    "participant_type": "bot",
                    "participant_name": "AI Agent",
                    "joined_at": datetime.now(timezone.utc).isoformat(),
                    "left_at": (datetime.now(timezone.utc) + timedelta(minutes=random.randint(1, 30))).isoformat(),
                    "duration_seconds": random.randint(60, 1800)
                }
            ]

            for participant in participants:
                try:
                    supabase.table("call_participants").insert(participant).execute()
                    participants_created += 1
                except Exception as e:
                    print(f"Error creating participant: {e}")

            # Add events
            events = [
                {
                    "call_id": call_id,
                    "event_type": "participant_joined",
                    "event_data": {"participant_id": participants[0]["participant_id"], "type": "user"},
                    "timestamp": datetime.now(timezone.utc).isoformat()
                },
                {
                    "call_id": call_id,
                    "event_type": "transcript_received",
                    "event_data": {"text": "Hello, how can I help you today?", "speaker": "bot"},
                    "timestamp": (datetime.now(timezone.utc) + timedelta(seconds=5)).isoformat()
                },
                {
                    "call_id": call_id,
                    "event_type": "participant_left",
                    "event_data": {"participant_id": participants[0]["participant_id"], "reason": "normal"},
                    "timestamp": (datetime.now(timezone.utc) + timedelta(minutes=10)).isoformat()
                }
            ]

            for event in events:
                try:
                    supabase.table("call_events").insert(event).execute()
                    events_created += 1
                except Exception as e:
                    print(f"Error creating event: {e}")

        print(f"Created {participants_created} participants and {events_created} events!")

    except Exception as e:
        print(f"Error creating participants and events: {e}")

async def main():
    """Main function to create all sample data"""
    print("Starting call monitoring test data creation...")

    # Create sample calls
    await create_sample_calls(50)

    # Create sample participants and events
    await create_sample_participants_and_events()

    print("Test data creation completed!")
    print("\nYou can now:")
    print("1. Start the server: python server.py")
    print("2. Visit http://localhost:8000/calls to see the call history")
    print("3. Test the API endpoints at http://localhost:8000/docs")

if __name__ == "__main__":
    asyncio.run(main())
